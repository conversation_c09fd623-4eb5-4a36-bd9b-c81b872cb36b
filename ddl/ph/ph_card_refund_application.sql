create table ph_card_refund_application
(
    id           bigint unsigned auto_increment
        primary key,
    card_no      varchar(36)  default '' not null comment '病历号',
    name         varchar(50)  default '' not null comment '姓名',
    id_card_no   varchar(36)  default '' not null comment '身份证号',
    mobile       varchar(20)  default '' not null comment '手机号',
    cert_photo   varchar(255) default '' not null comment '证件照片',
    face_photo   varchar(255) default '' not null comment '登记人个人照片',
    open_id      varchar(36)  default '' not null comment '申请人微信ID',
    apply_time   datetime                not null comment '申请时间',
    audit_status tinyint(1)   default 0  not null comment '审核状态, 0: 待审核, 1: 审核通过, 2: 已清退，3: 驳回',
    audit_time   datetime                null comment '审核时间',
    audit_user   varchar(50)  default '' null comment '审核人',
    audit_remark varchar(255) default '' null comment '审核备注'
)
    comment '就诊卡余额退款申请';

create index card_refund_application_card_no_index
    on ph_card_refund_application (card_no);

create index card_refund_application_open_id_index
    on ph_card_refund_application (open_id);

create index card_refund_application_id_card_no_index
    on ph_card_refund_application (id_card_no);
