package space.lzhq.ph.controller

import org.dromara.hutool.core.date.DateUtil
import org.dromara.hutool.http.server.servlet.ServletUtil
import com.alipay.api.response.AlipayTradeCreateResponse
import com.ruoyi.common.constant.Constants
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.system.service.ISysConfigService
import org.mospital.alipay.AlipayService
import org.mospital.alipay.AlipaySetting
import org.mospital.neusoft.request.GetClinicBillRequest
import org.mospital.neusoft.request.GetDayExpenseRequest
import org.mospital.neusoft.request.GetPatientExpenseRequest
import org.mospital.neusoft.request.QueryInHospitalListRequest
import org.mospital.neusoft.util.NeusoftService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.annotation.RequireActivePatient
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.common.ClientType
import space.lzhq.ph.common.PaymentKit
import space.lzhq.ph.common.ServiceType
import space.lzhq.ph.domain.AlipayPayment
import space.lzhq.ph.domain.AlipayRefund
import space.lzhq.ph.domain.ZhuyuanRefundForm
import space.lzhq.ph.ext.HisExt
import space.lzhq.ph.ext.WeixinExt
import space.lzhq.ph.ext.getCurrentZhuYuanPatient
import space.lzhq.ph.ext.toAjaxResult
import space.lzhq.ph.service.*
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@Suppress("DuplicatedCode")
@RestController
@RequestMapping("/api/zhuyuan")
class ZhuyuanController : BaseController() {

    @Autowired
    private lateinit var sysConfigService: ISysConfigService

    @Autowired
    private lateinit var wxPaymentService: IWxPaymentService

    @Autowired
    private lateinit var wxRefundService: IWxRefundService

    @Autowired
    private lateinit var alipayPaymentService: IAlipayPaymentService

    @Autowired
    private lateinit var alipayRefundService: IAlipayRefundService


    @Autowired
    private lateinit var pacsReportService: IPacsReportService


    @GetMapping("activePatient")
    @RequireSession
    fun activePatient(): AjaxResult {
        val currentPatient: space.lzhq.ph.domain.ZhuyuanPatient = request.getCurrentZhuYuanPatient()
        return AjaxResult.success(currentPatient)
    }

    @GetMapping("zhuyuanBalance")
    @RequireActivePatient(shouldBeZhuyuanPatient = true)
    fun getZhuyuanBalance(): AjaxResult {
        val currentPatient = request.getCurrentZhuYuanPatient()

        val request = GetPatientExpenseRequest().apply {
            this.queryId = currentPatient.zhuyuanNo
        }
        val result = NeusoftService.me.execute(request)

        return if (result.isFail) {
            AjaxResult.error("查询住院余额失败")
        } else {
            AjaxResult.success("操作成功", result.remainAmt.orEmpty())
        }
    }

    /**
     * 获取住院的费用清单
     * @param date 清单日
     */
    @GetMapping("fee")
    @RequireActivePatient(shouldBeZhuyuanPatient = true)
    fun fee(
        @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) date: LocalDate?,
    ): AjaxResult {
        val activePatient = request.getCurrentZhuYuanPatient()

        val dayExpenseRequest = GetDayExpenseRequest().apply {
            this.queryId = activePatient.zhuyuanNo
            this.date = date?.format(DateTimeFormatter.ISO_DATE).orEmpty()
        }
        val dayExpenseResult = NeusoftService.me.execute(dayExpenseRequest)

        return if (dayExpenseResult.isOk) {
            val dataList = dayExpenseResult.dataList
                .sortedByDescending { DateUtil.parse(it.feeDate) }
                .groupBy { it.feeDate }
                .map {
                    mapOf(
                        "date" to it.key,
                        "sumAmount" to it.value.sumOf { item -> item.amount?.toBigDecimalOrNull() ?: BigDecimal.ZERO },
                        "list" to it.value
                    )
                }
            val usedAmount: BigDecimal = dayExpenseResult.dataList.sumOf { it.amount?.toBigDecimalOrNull() ?: BigDecimal.ZERO }
            val result = mapOf("usedAmt" to usedAmount, "dataList" to dataList)
            AjaxResult.success(result)
        } else {
            dayExpenseResult.toAjaxResult()
        }
    }

    /**
     * 获取住院的账单
     */
    @GetMapping("bill")
    @RequireActivePatient(shouldBeZhuyuanPatient = true)
    fun bill(): AjaxResult {
        val activePatient = request.getCurrentZhuYuanPatient()

        val request = GetClinicBillRequest().apply {
            this.realName = activePatient.name
            this.queryId = activePatient.zhuyuanNo
            this.type = "2"
        }
        val execute = NeusoftService.me.execute(request)
        if (execute.isFail) return execute.toAjaxResult()

        val data = execute.content.sortedByDescending { DateUtil.parse(it.date) }
        return execute.toAjaxResult().data(data)
    }


    /**
     * 充值
     * @param amount 充值金额，以分为单位
     */
    @PostMapping("wxpay")
    @RequireActivePatient(shouldBeZhuyuanPatient = true)
    fun wxpay(
        @RequestParam amount: Long,
    ): AjaxResult {
        if (!sysConfigService.getBoolean("bool-wxxcx-zhuyuan-chongzhi", true)) {
            return AjaxResult.error("住院充值功能正在升级维护，暂时使用，敬请谅解")
        }

        if (amount < 1) {
            return AjaxResult.error("最小充值金额为0.01元")
        }

        val activePatient = request.getCurrentZhuYuanPatient()

        val inHospitalListRequest = QueryInHospitalListRequest().apply {
            this.realName = activePatient.name
            this.idCard = activePatient.idCardNo
        }
        val inHospitalPatientResult = NeusoftService.me.execute(inHospitalListRequest)
        if (inHospitalPatientResult.isFail) return AjaxResult.error("住院历史记录查询失败: ${inHospitalPatientResult.message}")
        val hospitalPatient = inHospitalPatientResult.dataList.find { it.inpatientNo == activePatient.zhuyuanNo }
        if (hospitalPatient?.status != 1) {
            return AjaxResult.error("当前绑定住院号状态为出院，不允许充值！")
        }

        val ip: String = ServletUtil.getClientIP(request)
        val serviceType = ServiceType.ZY
        val wxPayMpOrderResult = WeixinExt.pay(
            serviceType = serviceType,
            amount = amount.toInt(),
            ip = ip,
            patient = activePatient,
            expireTime = LocalDateTime.now().plusSeconds(90)
        ) { request, _ ->
            wxPaymentService.createAndSave(serviceType, activePatient, request)
        }
        return AjaxResult.success(wxPayMpOrderResult)
    }


    /**
     * 支付宝充值
     * @param amount 充值金额，以分为单位
     */
    @PostMapping("alipay")
    @RequireSession(clientType = ClientType.ALIPAY)
    @RequireActivePatient(shouldBeZhuyuanPatient = true)
    fun alipay(
        @RequestParam amount: Long,
    ): AjaxResult {
        if (!sysConfigService.getBoolean("bool-zfbxcx-zhuyuan-chongzhi", true)) {
            return AjaxResult.error("住院充值功能正在升级维护，暂停使用，敬请谅解")
        }

        if (amount < 100) {
            return AjaxResult.error("最小充值金额为1元")
        }

        if (amount > 500000) {
            return AjaxResult.error("最大充值金额为5000元")
        }

        val activePatient = request.getCurrentZhuYuanPatient()

        val serviceType = ServiceType.ZY
        val outTradeNo = PaymentKit.newOrderId(serviceType)
        val remark = AlipaySetting.maMerchantName + '-' + serviceType.description
        val alipayPayment = AlipayPayment(
            activePatient,
            serviceType,
            outTradeNo,
            amount,
            remark
        )
        val ok = 1 == alipayPaymentService.insertAlipayPayment(alipayPayment)
        if (!ok) {
            return AjaxResult.error("操作失败，请稍后重试")
        }
        val alipayTradeCreateResponse: AlipayTradeCreateResponse = AlipayService.createOrder(
            totalAmount = PaymentKit.fenToYuan(amount),
            openid = activePatient.openId,
            outTradeNo = outTradeNo,
            subject = remark,
            body = remark
        )
        return if (alipayTradeCreateResponse.isSuccess) {
            AjaxResult.success(alipayTradeCreateResponse)
        } else {
            AjaxResult.error(alipayTradeCreateResponse.subCode + "-" + alipayTradeCreateResponse.subMsg)
        }
    }

    @PostMapping("refreshAlipayOrder")
    @RequireSession(clientType = ClientType.ALIPAY)
    fun refreshAlipayOrder(@RequestParam outTradeNo: String): AjaxResult {
        synchronized(outTradeNo.intern()) {
            val payment = alipayPaymentService.selectAlipayPaymentByOutTradeNo(outTradeNo)
                ?: return AjaxResult.error("订单不存在[$outTradeNo]")

            val queryOrderResponse = AlipayService.queryOrder(payment.outTradeNo, "")
            alipayPaymentService.updateOnPay(payment, queryOrderResponse)

            HisExt.rechargeByAlipay(payment.id)

            return AjaxResult.success(alipayPaymentService.selectAlipayPaymentById(payment.id))
        }
    }

    private fun refundAlipay(refundForm: ZhuyuanRefundForm): AjaxResult {
        val cents: Long = PaymentKit.yuanToFen(refundForm.amount)
        if (cents <= 0L) {
            return AjaxResult.error("退款金额必须大于0")
        }

        val payment: AlipayPayment = alipayPaymentService.selectAlipayPaymentByOutTradeNo(refundForm.orderNumber)
            ?: return AjaxResult.error("订单不存在[${refundForm.orderNumber}]")

        if (!payment.isZhuyuan) {
            return AjaxResult.error("此订单不是住院订单")
        }

        if (payment.hisTradeStatus != Constants.RECHARGE_OK) {
            return AjaxResult.error("此订单状态异常，不支持退款")
        }

        if (cents > payment.unrefund) {
            return AjaxResult.error("退款金额超出订单限制")
        }

        val refund = AlipayRefund(payment, refundForm)
        alipayRefundService.insertAlipayRefund(refund)

        payment.exrefund = payment.exrefund + cents
        payment.unrefund = payment.totalAmount - payment.exrefund
        alipayPaymentService.updateAlipayPayment(payment)

        alipayRefundService.refundAlipay(refund)
        return AjaxResult.success(
            mapOf(
                "patientId" to refund.patientId,
                "zhuyuanNo" to refund.zhuyuanNo,
                "totalAmount" to PaymentKit.fenToYuan(payment.totalAmount),
                "refundAmount" to PaymentKit.fenToYuan(refund.amount),
                "zyPayNo" to refund.outTradeNo,
                "zyRefundNo" to refund.outRefundNo,
                "tradeNo" to refund.tradeNo,
            )
        )
    }


}
