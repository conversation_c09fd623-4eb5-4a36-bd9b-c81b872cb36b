package space.lzhq.ph.controller

import com.ruoyi.common.annotation.Log
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.core.page.TableDataInfo
import com.ruoyi.common.enums.BusinessType
import com.ruoyi.common.utils.poi.ExcelUtil
import org.apache.shiro.authz.annotation.RequiresPermissions
import org.mospital.neusoft.request.QueryPatientRequest
import org.mospital.neusoft.result.QueryPatientResult
import org.mospital.neusoft.util.NeusoftService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.ui.ModelMap
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.domain.Patient
import space.lzhq.ph.domain.Session
import space.lzhq.ph.service.IPatientService
import space.lzhq.ph.service.ISessionService


@Controller
@RequestMapping("/ph/patient")
class PatientAdminController : BaseController() {

    @Autowired
    private lateinit var service: NeusoftService

    @Autowired
    private lateinit var patientService: IPatientService

    @Autowired
    private lateinit var sessionService: ISessionService

    @RequiresPermissions("ph:patient:view")
    @GetMapping
    fun patient(): String {
        return "ph/patient/patient"
    }

    @RequiresPermissions("ph:patient:list")
    @PostMapping("/list")
    @ResponseBody
    fun list(patient: Patient): TableDataInfo {
        startPage()
        val list: List<Patient?> = patientService.selectPatientList(patient)
        return getDataTable(list)
    }

    @RequiresPermissions("ph:patient:export")
    @Log(title = "患者", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    fun export(patient: Patient): AjaxResult {
        val list = patientService.selectPatientList(patient)
        val util: ExcelUtil<Patient> = ExcelUtil<Patient>(Patient::class.java)
        return util.exportExcel(list, "patient")
    }

    @GetMapping("/add")
    fun add(): String {
        return "ph/patient/add"
    }

    @RequiresPermissions("ph:patient:add")
    @Log(title = "患者", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    fun addSave(patient: Patient): AjaxResult {
        return toAjax(patientService.insertPatient(patient))
    }

    @GetMapping("/edit/{id}")
    fun edit(@PathVariable id: Long, mmap: ModelMap): String {
        val patient = patientService.selectPatientById(id)
        mmap["patient"] = patient
        return "ph/patient/edit"
    }

    @RequiresPermissions("ph:patient:edit")
    @Log(title = "患者", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    fun editSave(patient: Patient): AjaxResult {
        return toAjax(patientService.updatePatient(patient))
    }

    @RequiresPermissions("ph:patient:remove")
    @Log(title = "患者", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    fun remove(ids: String): AjaxResult {
        return toAjax(patientService.deletePatientByIds(ids))
    }

    @RequiresPermissions("ph:patient:showSid")
    @GetMapping("/showSid/{id}")
    @ResponseBody
    fun showSid(@PathVariable id: Long): AjaxResult {
        val patient = patientService.selectPatientById(id)
        val session: Session? = sessionService.selectSessionByOpenId(patient.openId)
        return AjaxResult.warn(session?.getId() ?: "无")
    }

    @RequiresPermissions("ph:patient:queryHisPatient")
    @GetMapping("/queryHisPatient/{jzCardNo}")
    @ResponseBody
    fun queryHisPatient(@PathVariable jzCardNo: String): AjaxResult {
        val request = QueryPatientRequest().apply {
            patientCard = jzCardNo
        }
        val patientInfoResult: QueryPatientResult = service.execute(request)
        return if (patientInfoResult.isFail) {
            AjaxResult.error(patientInfoResult.message)
        } else {
            AjaxResult.success(patientInfoResult)
        }
    }

}