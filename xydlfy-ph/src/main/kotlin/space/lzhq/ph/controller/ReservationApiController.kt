package space.lzhq.ph.controller

import cn.doit.zhangyi.neusoft.bean.json.MipSettleCallBackRequest
import cn.doit.zhangyi.neusoft.bean.json.MipSettleRequest
import cn.doit.zhangyi.neusoft.bean.json.MipSettleResponse
import org.dromara.hutool.core.date.*
import org.dromara.hutool.core.math.NumberUtil
import org.dromara.hutool.crypto.digest.MD5
import org.dromara.hutool.http.server.servlet.ServletUtil
import com.alibaba.fastjson.JSONObject
import com.github.binarywang.wxpay.bean.request.WxInsurancePayUnifiedOrderRequest
import com.github.binarywang.wxpay.bean.request.WxPayRefundRequest
import com.github.binarywang.wxpay.bean.result.WxInsurancePayUnifiedOrderResult
import com.github.binarywang.wxpay.bean.result.WxPayRefundResult
import com.github.binarywang.wxpay.service.WxInsurancePayService
import com.ruoyi.common.config.ServerConfig
import com.ruoyi.common.constant.Constants
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.system.service.ISysConfigService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.launch
import org.mospital.alipay.HospitalOrderStatus
import org.mospital.jackson.JacksonKit
import org.mospital.neusoft.mip.HisMipBaseRequest
import org.mospital.neusoft.mip.MipRefundRequest
import org.mospital.neusoft.request.*
import org.mospital.neusoft.result.CancelSubscriptionResult
import org.mospital.neusoft.result.FindOutpatientPayResult
import org.mospital.neusoft.service.NeusoftMipPayService
import org.mospital.neusoft.util.NeusoftService
import org.mospital.wecity.mip.WecityMipService
import org.mospital.wecity.mip.WeixinMipSetting
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.annotation.RequireActivePatient
import space.lzhq.ph.common.*
import space.lzhq.ph.common.ClientType
import space.lzhq.ph.common.PaymentKit.newRefundNo
import space.lzhq.ph.configuration.WxSubscribeMessageConfiguration
import space.lzhq.ph.domain.*
import space.lzhq.ph.domain.Reservation
import space.lzhq.ph.domain.Reservation.*
import space.lzhq.ph.ext.*
import space.lzhq.ph.service.*
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.*

@RestController
@RequestMapping("/api/reservation")
class ReservationApiController : BaseController() {

    @Autowired
    private lateinit var subscribeMessageConfiguration: WxSubscribeMessageConfiguration

    @Autowired
    private lateinit var sysConfigService: ISysConfigService

    @Autowired
    private lateinit var reservationService: IReservationService

    @Autowired
    private lateinit var wxPaymentService: IWxPaymentService

    @Autowired
    private lateinit var wxRefundService: IWxRefundService

    @Autowired
    private lateinit var alipayPaymentService: IAlipayPaymentService

    @Autowired
    private lateinit var alipayRefundService: IAlipayRefundService

    @Autowired
    private lateinit var service: NeusoftService

    @Autowired
    private lateinit var queueCallingService: QueueCallingService

    @Autowired
    private lateinit var mipWxOrderService: IMipWxOrderService

    @Autowired
    private lateinit var wxInsurancePayService: WxInsurancePayService

    @Autowired
    private lateinit var traceService: ITraceService

    @Autowired
    private lateinit var serverConfig: ServerConfig

    /**
     * 挂号校验
     */
    fun validateReserve(
        currentPatient: Patient,
        birthday: DateTime,
        departmentCode: String,
        departmentName: String,
    ) {
        val sexCode = currentPatient.gender
        val sexName = if (sexCode == 1) "女性" else if (sexCode == 0) "男性" else "未知"
        val ageOfYear: Int = DateUtil.ageOfNow(birthday)
        val ageOfDay = DateUtil.betweenDay(birthday, Date(), true)

        // 仅允许女性挂的门诊
        val gynecologyDepartment = sysConfigService.gynecologyDepartment
        if (gynecologyDepartment.contains(departmentCode) || gynecologyDepartment.contains(departmentName)) {
            check(sexCode == 1) { "签约卡性别为${sexName},${departmentName} 科室仅允许女性挂号！" }
        }

        // 仅允许男性挂的门诊
        val manDepartment = sysConfigService.manDepartment
        if (manDepartment.contains(departmentCode) || manDepartment.contains(departmentName)) {
            check(sexCode == 0) { "签约卡性别为${sexName},${departmentName} 科室仅允许男性挂号！" }
        }

        // 仅允许年龄小于等于 28 天才能挂的门诊。新生儿科
        val neonatologyDepartment = sysConfigService.selectConfigByKey("ph.lt28DayDepartment")
            .split(",").map { it.trim() }
        if (neonatologyDepartment.contains(departmentCode) || neonatologyDepartment.contains(departmentName)) {
            check(ageOfDay <= 28) { "挂${departmentName}要求小于等于年龄28天！" }
        }

        // 仅允许16岁以下才能挂号的门诊：儿保科
        val ageUnder16YearsOldDepartments = sysConfigService.selectConfigByKey("ph.under16YearsOldDepartment")
            .split(",").map { it.trim() }
        if (ageUnder16YearsOldDepartments.contains(departmentCode) || ageUnder16YearsOldDepartments.contains(departmentName)) {
            check(ageOfYear <= 16) { "挂${departmentName}要求年龄小于16岁！" }
        }

        // 仅允许年龄区间 28天到18岁能挂的门诊
        val age28DayTo18Year = sysConfigService.selectConfigByKey("ph.age28DayTo18YearDepartment")
            .split(",").map { it.trim() }
        if (age28DayTo18Year.contains(departmentCode) || age28DayTo18Year.contains(departmentName)) {
            check(ageOfDay >= 28 && ageOfYear <= 18) { "挂${departmentName}要求年龄在28天~18岁区间！" }
        }

        // 仅允许年龄区间 35-64岁才能的门诊。挂宫颈癌筛查门诊
        val age35YearTo64Year = sysConfigService.selectConfigByKey("ph.age35YearTo64YearDepartment")
            .split(",").map { it.trim() }
        if (age35YearTo64Year.contains(departmentCode) || age35YearTo64Year.contains(departmentName)) {
            check(ageOfYear in 35..64) { "挂${departmentName}要求年龄在35岁~64岁区间！" }
        }

        // 仅允许年龄区间 35-44岁才能的门诊。挂乳腺癌筛查门诊
        val age35YearTo44Year = sysConfigService.selectConfigByKey("ph.age35YearTo44YearDepartment")
            .split(",").map { it.trim() }
        if (age35YearTo44Year.contains(departmentCode) || age35YearTo44Year.contains(departmentName)) {
            check(ageOfYear in 35..44) { "挂${departmentName}要求年龄在35岁~44岁区间！" }
        }
    }

    /**
     * @param date eg. 2024/11/8 0:00:00
     * @param clinicTime eg. 上午
     */
    private fun createReservationV1(
        date: String,
        clinicTime: String,
        scheduleId: String,
        sourceNo: String,
        departmentCode: String,
        doctorCode: String,
        paymentType: Int
    ): Reservation {
        val currentPatient: Patient = request.getCurrentPatient()
        if (currentPatient.clientTypeEnum == ClientType.WEIXIN &&
            !sysConfigService.getBoolean("bool-wxxcx-yuyue-guahao", true)
        ) {
            throw RuntimeException("预约挂号功能正在升级维护，暂停使用，敬请谅解")
        }
        if (currentPatient.clientTypeEnum == ClientType.ALIPAY &&
            !sysConfigService.getBoolean("bool-zfbxcx-yuyue-guahao", true)
        ) {
            throw RuntimeException("预约挂号功能正在升级维护，暂停使用，敬请谅解")
        }

        val jzLocalDate: LocalDate = try {
            DateUtil.parse(date, "yyyy/MM/dd HH:mm:ss").toLocalDateTime().toLocalDate()
        } catch (e: Exception) {
            logger.error(e.message, e)
            throw RuntimeException("解析就诊日期失败")
        }
        val ticketsResult = NeusoftService.me.execute(
            request = QueryScheduleTimeRequest().apply {
                this.departmentCode = departmentCode
                this.clinicDate = jzLocalDate.format(DatePattern.NORM_DATE_FORMATTER)
                this.clinicTime = clinicTime
                this.doctorCode = doctorCode
                this.scheduleId = scheduleId
            }
        )
        require(ticketsResult.isOk) { ticketsResult.message }

        val ticket = ticketsResult.content.firstOrNull { it.sourceNo == sourceNo }
        requireNotNull(ticket) { "指定号源不可用" }
        require(ticket.haveNo == "1") { "指定号源不可用" }

        val jzLocalTime = LocalTime.parse(ticket.visitTime, DateTimeFormatter.ofPattern("HH:mm"))
        val jzLocalDateTime = LocalDateTime.of(jzLocalDate, jzLocalTime)
        require(jzLocalDateTime > LocalDateTime.now()) { "就诊时间已过，指定号源不可用" }

        val doctorsResult = NeusoftService.me.execute(
            QueryRealtimeScheduleDetailRequest().apply {
                this.clinicDate = jzLocalDate.format(DatePattern.NORM_DATE_FORMATTER)
                this.departmentCode = departmentCode
            }
        )
        require(doctorsResult.isOk) { doctorsResult.message }
        val doctorSchedule = doctorsResult.content.firstOrNull { it.scheduleId == scheduleId }
        requireNotNull(doctorSchedule) { "指定排班不可用" }

        val existList = reservationService.lambdaQuery()
            .eq(Reservation::getJzDate, DateUtil.date(jzLocalDateTime))
            .eq(Reservation::getDepartmentCode, departmentCode)
            .eq(Reservation::getPatientId, currentPatient.jzCardNo)
            .eq(Reservation::getOperationType, OPERATION_TYPE_RESERVE)
            .eq(Reservation::getOperationDesc, "预约成功")
            .list()

        //如果subscriptionId存在两条一样的数据，那么就一条预约一条取消预约
        if (existList.isNotEmpty()) {
            throw RuntimeException("您在[${doctorSchedule.departmentName}]已存在[$doctorSchedule.doctorName]的预约记录，请勿重复预约")
        }

        try {
            val birthDate: DateTime = DateTime(HisExt.getPatientBirthday(currentPatient.idCardNo, currentPatient.jzCardNo))
            validateReserve(currentPatient, birthDate, departmentCode, doctorSchedule.departmentName)
        } catch (e: Exception) {
            throw RuntimeException(e.message)
        }

        val clinicFee: String = HisExt.getClinicFee(
            patient = currentPatient,
            clinicFee = doctorSchedule.clinicFee,
            underRegFee = doctorSchedule.underRegFee,
            overRegFee = doctorSchedule.overRegFee
        )

        val requestInsertSubscription = InsertSubscriptionRequest().apply {
            this.scheduleId = scheduleId
            this.sourceNo = sourceNo
            this.sourceOrder = ticket.sourceOrder.toInt()
            this.realName = currentPatient.name
            this.idCard = currentPatient.idCardNo
            this.patientCard = currentPatient.jzCardNo
            this.departmentName = doctorSchedule.departmentName
            this.departmentCode = departmentCode
            this.doctorName = doctorSchedule.doctorName
            this.doctorCode = doctorCode
            this.clinicType = doctorSchedule.clinicType
            this.clinicFee = clinicFee
            // 就诊日期参数。注意末尾的空格是必要的！！
            this.clinicDate = jzLocalDateTime.format(DateTimeFormatter.ofPattern("yyyy/M/d "))
            this.clinicTime = jzLocalDateTime.format(DateTimeFormatter.ofPattern("HH:mm:ss"))
        }

        val reservation = Reservation(currentPatient)
        reservation.operationType = OPERATION_TYPE_RESERVE
        reservation.operationTime = DateUtil.now().setField(DateField.MILLISECOND, 0)
        reservation.jzDate = DateUtil.date(jzLocalDateTime)
        reservation.reservationType = doctorSchedule.clinicType
        reservation.departmentCode = departmentCode
        reservation.departmentName = doctorSchedule.departmentName
        reservation.doctorCode = doctorCode
        reservation.doctorName = doctorSchedule.doctorName
        reservation.status = RESERVATION_STATUS_INIT
        reservation.jzTime = DateUtil.date(jzLocalDateTime)
        reservation.departmentLocation = doctorSchedule.departmentAddress
        reservation.visitNumber = sourceNo
        reservation.reservationNumber = ticket.sourceOrder
        reservation.subscriptionJson = JacksonKit.writeValueAsString(requestInsertSubscription)
        reservation.operationResult = OPERATION_RESULT_INIT
        reservation.operationDesc = ""
        reservation.scheduleId = scheduleId
        reservation.subscriptionId = ""
        reservation.clinicFee = clinicFee.toBigDecimal()
        reservation.paymentStatus = if (NumberUtil.equals(clinicFee.toBigDecimal(), BigDecimal.ZERO)) {
            PAYMENT_STATUS_FREE_OF_CHARGE
        } else {
            PAYMENT_STATUS_UNPAID
        }
        reservation.paymentType = paymentType
        val ok = reservationService.save(reservation)
        if (!ok) {
            throw RuntimeException("创建挂号订单失败，请重试")
        }
        return reservation
    }

    /**
     * @param date eg. 2024/11/8 0:00:00
     * @param clinicTime eg. 上午
     */
    @PostMapping("reserveByWeChatPay/v1")
    @RequireActivePatient
    fun reserveByWeChatPayV1(
        @RequestParam("clinicDate") date: String,
        @RequestParam scheduleId: String,
        @RequestParam sourceNo: String,
        @RequestParam departmentCode: String,
        @RequestParam doctorCode: String,
        @RequestParam clinicTime: String,
    ): AjaxResult {
        val currentPatient: Patient = request.getCurrentPatient()

        var reservation = try {
            createReservationV1(
                date = date,
                clinicTime = clinicTime,
                scheduleId = scheduleId,
                sourceNo = sourceNo,
                departmentCode = departmentCode,
                doctorCode = doctorCode,
                paymentType = PAYMENT_TYPE_WECHAT,
            )
        } catch (e: Exception) {
            return AjaxResult.error(e.message)
        }

        reservation = HisExt.insertSubscription(reservation.id) ?: return AjaxResult.error("预约失败")
        if (reservation.operationResult == OPERATION_RESULT_FAIL) {
            return AjaxResult.error(reservation.operationDesc)
        }
        if (NumberUtil.equals(reservation.clinicFee, BigDecimal.ZERO)) {
            traceService.traceReservation(reservation.id, "费用为0，无需支付")
            return AjaxResult.success(
                mapOf(
                    "needPay" to 0,
                    "result" to 1
                )
            )
        }

        val outpatientPayResult: FindOutpatientPayResult? = try {
            NeusoftService.me.execute(
                request = FindOutpatientPayRequest().apply {
                    this.realName = currentPatient.name
                    this.patientCard = currentPatient.patientNo
                }
            )
        } catch (e: Exception) {
            traceService.traceReservation(reservation.id, "查询待缴费清单失败：${e.stackTraceToString()}")
            logger.error(e.message, e)
            null
        }
        val outpatientPay = outpatientPayResult?.content?.firstOrNull { it.clinicNo == reservation.subscriptionId }
        if (outpatientPay == null) {
            traceService.traceReservation(
                reservation.id,
                "未查询到匹配的待缴费清单：${JacksonKit.writeValueAsString(outpatientPayResult ?: "null")}"
            )
            HisExt.releaseSubscription(
                reservationId = reservation.id,
                scheduleId = reservation.scheduleId,
                subscriptionId = reservation.subscriptionId
            )
            return AjaxResult.error("未查询到预约订单")
        }

        try {
            reservation.prescriptionNo = outpatientPay.prescriptionNo
            reservationService.lambdaUpdate()
                .set(Reservation::getPrescriptionNo, reservation.prescriptionNo)
                .eq(Reservation::getId, reservation.id)
                .update()

            val ip: String = ServletUtil.getClientIP(request)
            val serviceType = ServiceType.GH
            val wxPayMpOrderResult = WeixinExt.pay(
                serviceType = serviceType,
                amount = PaymentKit.yuanToFen(reservation.clinicFee).toInt(),
                ip = ip,
                patient = currentPatient,
                expireTime = LocalDateTime.now().plusSeconds(90)
            ) { request, _ ->
                val wxPayment: WxPayment? = wxPaymentService.createAndSaveWithBusinessSerialNumber(
                    serviceType,
                    currentPatient,
                    request,
                    reservation.id.toString()
                )
                if (wxPayment != null) {
                    reservationService.lambdaUpdate()
                        .set(Reservation::getPaymentId, wxPayment.id)
                        .eq(Reservation::getId, reservation.id)
                        .update()
                }
            }
            traceService.traceReservation(reservation.id, "微信支付下单成功")
            return AjaxResult.success(mapOf(
                "appId" to wxPayMpOrderResult.appId,
                "timeStamp" to wxPayMpOrderResult.timeStamp,
                "nonceStr" to wxPayMpOrderResult.nonceStr,
                "packageValue" to wxPayMpOrderResult.packageValue,
                "signType" to wxPayMpOrderResult.signType,
                "paySign" to wxPayMpOrderResult.paySign,
                "reservationId" to reservation.id
            ))
        } catch (e: Exception) {
            logger.error(e.message, e)
            traceService.traceReservation(reservation.id, "微信支付下单失败：${e.stackTraceToString()}")
            HisExt.releaseSubscription(
                reservationId = reservation.id,
                scheduleId = reservation.scheduleId,
                subscriptionId = reservation.subscriptionId
            )
            return AjaxResult.error("预约失败")
        }
    }

    /**
     * @param date eg. 2024/11/8 0:00:00
     * @param clinicTime eg. 上午
     */
    @PostMapping("reserveByMip/v1")
    @RequireActivePatient
    fun reserveByMipV1(
        @RequestParam("clinicDate") date: String,
        @RequestParam scheduleId: String,
        @RequestParam sourceNo: String,
        @RequestParam departmentCode: String,
        @RequestParam doctorCode: String,
        @RequestParam clinicTime: String,
    ): AjaxResult {
        if (!sysConfigService.getBoolean("bool-wxxcx-yibao-yidong-zhifu", true)) {
            return AjaxResult.error("医保移动支付功能正在升级维护，暂停使用，敬请谅解")
        }

        var reservation = try {
            createReservationV1(
                date = date,
                clinicTime = clinicTime,
                scheduleId = scheduleId,
                sourceNo = sourceNo,
                departmentCode = departmentCode,
                doctorCode = doctorCode,
                paymentType = PAYMENT_TYPE_WECHAT_MIP
            )
        } catch (e: Exception) {
            return AjaxResult.error(e.message)
        }

        reservation = HisExt.insertSubscription(reservation.id) ?: return AjaxResult.error("预约失败")
        if (reservation.operationResult == OPERATION_RESULT_FAIL) {
            return AjaxResult.error(reservation.operationDesc)
        }
        if (NumberUtil.equals(reservation.clinicFee, BigDecimal.ZERO)) {
            traceService.traceReservation(reservation.id, "费用为0，无需支付")
            return AjaxResult.success(
                mapOf(
                    "needPay" to 0,
                    "result" to 1
                )
            )
        }

        val currentPatient: Patient = request.getCurrentPatient()
        val outpatientPayResult: FindOutpatientPayResult = try {
            NeusoftService.me.execute(
                request = FindOutpatientPayRequest().apply {
                    this.realName = currentPatient.name
                    this.patientCard = currentPatient.patientNo
                }
            )
        } catch (e: Exception) {
            traceService.traceReservation(reservation.id, "查询待缴费清单失败：${e.stackTraceToString()}")
            HisExt.releaseSubscription(
                reservationId = reservation.id,
                scheduleId = reservation.scheduleId,
                subscriptionId = reservation.subscriptionId
            )
            logger.error(e.message, e)
            return AjaxResult.error("查询待缴费清单失败")
        }
        val outpatientPay = outpatientPayResult.content.firstOrNull { it.clinicNo == reservation.subscriptionId }
        if (outpatientPay == null) {
            traceService.traceReservation(
                reservation.id,
                "未查询到匹配的待缴费清单：${JacksonKit.writeValueAsString(outpatientPayResult)}"
            )
            HisExt.releaseSubscription(
                reservationId = reservation.id,
                scheduleId = reservation.scheduleId,
                subscriptionId = reservation.subscriptionId
            )
            return AjaxResult.error("未查询到预约订单")
        }

        reservation.prescriptionNo = outpatientPay.prescriptionNo
        reservationService.lambdaUpdate()
            .set(Reservation::getPrescriptionNo, reservation.prescriptionNo)
            .eq(Reservation::getId, reservation.id)
            .update()
        return AjaxResult.success(reservation)
    }

    @PostMapping("payReservationByMip")
    @RequireActivePatient
    fun payReservationByMip(
        @RequestParam reservationId: Long,
        @RequestParam mipAuthCode: String,
        @RequestParam(required = false, defaultValue = "1") acctUsedFlag: String,
    ): AjaxResult {
        val reservation = reservationService.getById(reservationId) ?: return AjaxResult.error("订单不存在")

        val currentPatient: Patient = request.getCurrentPatient()
        if (reservation.patientId != currentPatient.patientNo) {
            return AjaxResult.error("当前就诊人不能操作此订单")
        }

        if (reservation.operationType != OPERATION_TYPE_RESERVE) {
            traceService.traceReservation(reservation.id, "订单操作类型不正确: ${reservation.operationType}")
            return AjaxResult.error("订单操作类型不正确")
        }
        if (reservation.status != RESERVATION_STATUS_INIT) {
            traceService.traceReservation(reservation.id, "订单状态不正确: ${reservation.status}")
            return AjaxResult.error("订单状态不正确")
        }
        if (reservation.operationResult != OPERATION_RESULT_SUCCESS) {
            traceService.traceReservation(reservation.id, "订单操作结果不正确: ${reservation.operationResult}")
            return AjaxResult.error("订单操作结果不正确")
        }
        if (reservation.paymentType != PAYMENT_TYPE_WECHAT_MIP) {
            traceService.traceReservation(reservation.id, "订单支付方式不正确: ${reservation.paymentType}")
            return AjaxResult.error("订单支付方式不正确")
        }
        if (reservation.paymentStatus != PAYMENT_STATUS_UNPAID) {
            traceService.traceReservation(reservation.id, "订单支付状态不正确: ${reservation.paymentStatus}")
            return AjaxResult.error("订单支付状态不正确")
        }

        val userQueryResult = try {
            WecityMipService.queryUser(
                qrcode = mipAuthCode,
                openid = currentPatient.openId
            )
        } catch (e: Exception) {
            traceService.traceReservation(reservation.id, "查询用户医保信息失败：${e.stackTraceToString()}")
            logger.error(e.message, e)
            return AjaxResult.error("查询用户医保信息失败")
        }

        if (!userQueryResult.isOK()) {
            traceService.traceReservation(
                reservation.id,
                "查询用户医保信息失败：${JacksonKit.writeValueAsString(userQueryResult)}"
            )
            HisExt.releaseSubscription(
                reservationId = reservation.id,
                scheduleId = reservation.scheduleId,
                subscriptionId = reservation.subscriptionId
            )
            return AjaxResult.error(userQueryResult.message)
        }

        if (userQueryResult.cityId.isNullOrBlank()) {
            traceService.traceReservation(
                reservation.id,
                "查询用户参保地失败：${JacksonKit.writeValueAsString(userQueryResult)}"
            )
            HisExt.releaseSubscription(
                reservationId = reservation.id,
                scheduleId = reservation.scheduleId,
                subscriptionId = reservation.subscriptionId
            )
            return AjaxResult.error("获取医保参保地失败")
        }
        if (!userQueryResult.cityId!!.startsWith("65")) {
            traceService.traceReservation(
                reservation.id,
                "目前仅支持新疆维吾尔自治区医保：${JacksonKit.writeValueAsString(userQueryResult)}"
            )
            HisExt.releaseSubscription(
                reservationId = reservation.id,
                scheduleId = reservation.scheduleId,
                subscriptionId = reservation.subscriptionId
            )
            return AjaxResult.error("目前仅支持新疆维吾尔自治区医保")
        }
        if (userQueryResult.isSelfPay() && (userQueryResult.userCardNo != currentPatient.idCardNo || userQueryResult.userName != currentPatient.name)) {
            traceService.traceReservation(
                reservation.id,
                "医保信息与当前就诊人信息不一致：医保信息（${userQueryResult.userName}, ${userQueryResult.userCardNo}）vs. 当前就诊人信息（${currentPatient.name}, ${currentPatient.idCardNo}"
            )
            HisExt.releaseSubscription(
                reservationId = reservation.id,
                scheduleId = reservation.scheduleId,
                subscriptionId = reservation.subscriptionId
            )
            return AjaxResult.error("请在医保电子凭证服务授权页，选择【${currentPatient.name}】的医保账户")
        }

        val payAuthNo = if (userQueryResult.isSelfPay()) {
            userQueryResult.payAuthNo!!
        } else if (userQueryResult.isFamilyPay()) {
            userQueryResult.familyPayAuthNo!!
        } else {
            // Unreachable
            return AjaxResult.error("未知的医保支付方式")
        }

        val recipeFeeList = listOf(
            MipSettleRequest.RecipeFee(
                recipeNo = reservation.prescriptionNo,
                fee = reservation.clinicFee
            )
        )
        val mipSettleRequest = MipSettleRequest(
            clinicNo = reservation.subscriptionId,
            recipeFeeList = recipeFeeList,
            totalFee = reservation.clinicFee,
            payAuthNo = payAuthNo,
            psnName = currentPatient.name,
            psnIdenNo = currentPatient.idCardNo,
            insuCode = userQueryResult.cityId,
            acctUsedFlag = acctUsedFlag,
            uldLatlnt = userQueryResult.longitudeLatitude.toString()
        )
        val mipSettleRet =
            NeusoftMipPayService.mipSettle(request = HisMipBaseRequest(mipSettleRequest))

        if (mipSettleRet.isFail) {
            traceService.traceReservation(
                reservation.id,
                "门诊预结算M001失败：${JacksonKit.writeValueAsString(mipSettleRet)}"
            )
            HisExt.releaseSubscription(
                reservationId = reservation.id,
                scheduleId = reservation.scheduleId,
                subscriptionId = reservation.subscriptionId
            )
            return AjaxResult.error(mipSettleRet.message)
        }

        val mipWxOrder = MipWxOrder(userQueryResult, currentPatient, mipSettleRet.data!!).apply {
            this.clinicNo = mipSettleRequest.clinicNo
            this.settleCallbackRequest = JacksonKit.writeValueAsString(
                MipSettleCallBackRequest(
                    mipSettleRequest,
                    mipSettleRet.data as MipSettleResponse
                )
            )
        }
        mipWxOrderService.save(mipWxOrder)
        reservation.paymentId = mipWxOrder.id
        reservationService.lambdaUpdate()
            .set(Reservation::getPaymentId, mipWxOrder.id)
            .eq(Reservation::getId, reservation.id)
            .update()

        val unifiedOrderRequest: WxInsurancePayUnifiedOrderRequest = WxInsurancePayUnifiedOrderRequest().apply {
            this.orderType = org.mospital.wecity.mip.Constants.ORDER_TYPE_DiagPay
            this.openid = currentPatient.openId
            this.hospOutTradeNo = PaymentKit.newOrderId(ServiceType.YB)
            this.hospitalName = com.ruoyi.common.constant.Constants.MERCHANT_NAME
            // 总共需要支付现金金额，对应 feeSumamt
            this.totalFee = PaymentKit.yuanToFen(mipWxOrder.feeSumAmount).toInt()
            // 现金需要支付的金额，对应 ownPayAmt
            this.cashFee = PaymentKit.yuanToFen(mipWxOrder.ownPayAmount).toInt()
            this.payType =
                if (this.cashFee > 0) 3 else 2
            // 医保支付金额，医保基金支付+个人账户支出，对应 fundPay + psnAcctPay
            this.insuranceFee =
                PaymentKit.yuanToFen(mipWxOrder.fundPayAmount + mipWxOrder.personalAccountPayAmount).toInt()
            this.spbillCreateIp = ServletUtil.getClientIP(request)
            this.notifyUrl = serverConfig.onWxInsurancePayUrl
            this.returnUrl = "pages_yibao/OrderResult/OrderResult?hospOutTradeNo=${this.hospOutTradeNo}"
            this.body = hospitalName + "医保移动支付小程序"
            this.userCardType = 1
            this.userCardNo = MD5.of().digestHex(mipWxOrder.userCardNo)
            this.userName = mipWxOrder.userName
            this.longitude = mipWxOrder.longitudeLatitude.split(",")[0].trim()
            this.latitude = mipWxOrder.longitudeLatitude.split(",")[1].trim()
            this.gmtOutCreate = DateUtil.format(Date(), DatePattern.PURE_DATETIME_PATTERN)
            this.serialNo = mipWxOrder.hisOrderId
            this.payAuthNo = mipWxOrder.payAuthNo
            this.payOrderId = mipWxOrder.payOrderId
            this.cityId = WeixinMipSetting.ma.cityId
            this.orgNo = WeixinMipSetting.ma.orgCode
            this.channelNo = WeixinMipSetting.ma.channelNo
            if (!mipWxOrder.isSelfPay) {
                this.extends = JacksonKit.writeValueAsString(
                    mapOf(
                        "rel_user_name_md5" to MD5.of().digestHex(mipWxOrder.patientName),
                        "rel_user_card_no_md5" to MD5.of().digestHex(mipWxOrder.patientIdCardNo)
                    )
                )
            }
        }

        return try {
            val wxInsurancePayUnifiedOrderResult: WxInsurancePayUnifiedOrderResult =
                wxInsurancePayService.unifiedOrder(unifiedOrderRequest)

            val settleCallBackRequest =
                JacksonKit.readValue(mipWxOrder.settleCallbackRequest, MipSettleCallBackRequest::class.java)
            //通过下单回参构建 his回调结算请求体
            mipWxOrder.apply {
                this.medTransactionId = wxInsurancePayUnifiedOrderResult.medTransId
                this.hospitalOutTradeNo = unifiedOrderRequest.hospOutTradeNo
                this.cashOrderCreateTime = LocalDateTime.now()
                this.settleCallbackRequest = JacksonKit.writeValueAsString(settleCallBackRequest.apply {
                    thirdInfo = MipSettleCallBackRequest.ThirdInfoDetail(
                        unifiedOrderRequest.hospOutTradeNo,
                        wxInsurancePayUnifiedOrderResult.medTransId,
                        "98",
                        mipWxOrder.ownPayAmount,
                        DateUtil.formatDateTime(Date()),
                        null
                    )
                })
            }
            mipWxOrderService.updateById(mipWxOrder)
            AjaxResult.success(
                mapOf(
                    "medTransactionId" to wxInsurancePayUnifiedOrderResult.medTransId,
                    "payUrl" to wxInsurancePayUnifiedOrderResult.payUrl,
                    "payAppId" to wxInsurancePayUnifiedOrderResult.payAppId
                )
            )
        } catch (e: Exception) {
            traceService.traceReservation(reservation.id, "微信医保统一下单失败：${e.stackTraceToString()}")
            HisExt.releaseSubscription(
                reservationId = reservation.id,
                scheduleId = reservation.scheduleId,
                subscriptionId = reservation.subscriptionId
            )
            logger.error(e.message, e)
            AjaxResult.error(e.message)
        }
    }

    @PostMapping("releaseSubscription")
    @RequireActivePatient
    fun releaseSubscription(
        @RequestParam reservationId: Long,
    ): AjaxResult {
        val reservation = reservationService.getById(reservationId) ?: return AjaxResult.error("记录不存在")
        if (reservation.openid != request.getCurrentPatient().openId) {
            return AjaxResult.error("当前用户无权操作此订单")
        }

        try {
            HisExt.releaseSubscription(
                reservationId = reservation.id,
                scheduleId = reservation.scheduleId,
                subscriptionId = reservation.subscriptionId,
                waitUserPay = false
            )
            return AjaxResult.success()
        } catch (e: Exception) {
            logger.error(e.message, e)
            return AjaxResult.error(e.message)
        }

    }

    /**
     * 预约历史
     */
    @GetMapping("history")
    @RequireActivePatient
    fun history(
        @RequestParam(
            required = false
        ) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) startDate: LocalDate?,
        @RequestParam(
            required = false
        ) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) endDate: LocalDate?,
    ): AjaxResult {
        val currentPatient = request.getCurrentPatient()
        val startTime = startDate ?: LocalDate.now().minusMonths(3)
        val endTime = endDate ?: LocalDate.now().plusDays(1)
        if (TimeUtil.between(startTime.atStartOfDay(), endTime.atStartOfDay(), ChronoUnit.DAYS) < 0) {
            return AjaxResult.error("开始时间不能大于结束时间")
        }
        val request = SubscriptionHistoryRequest().apply {
            this.patientCard = currentPatient.jzCardNo
            this.startDate = startDate?.toString() ?: ""
            this.endDate = endDate?.toString() ?: ""
        }

        val result = service.execute(
            request = request
        )
        return if (result.isOk) {
            AjaxResult.success(result.content.sortedByDescending { "${it.clinicDate} ${it.clinicTime}" })
        } else {
            AjaxResult.error(result.message.ifBlank { "查询历史记录失败" })
        }
    }

    /**
     * 排队叫号
     */
    @GetMapping("findQueueUp")
    @RequireActivePatient
    fun findQueueUp(): AjaxResult {

        val patientWait = queueCallingService.patientWait("**********")
        return if (patientWait.getIntValue("resultCode") == 0) {
            val result = patientWait.getJSONArray("data")
                .associateBy { (it as JSONObject).getString("queue_type_name") }
            AjaxResult.success(result)
        } else {
            AjaxResult.error(patientWait.getString("resultMsg"))
        }
    }

    /**
     * 取消预约
     * @param subscriptionId 预约流水号
     */
    @PostMapping("cancel")
    @RequireActivePatient
    fun cancel(
        @RequestParam subscriptionId: String,
    ): AjaxResult {
        val currentPatient = request.getCurrentPatient()

        if (currentPatient.clientTypeEnum == ClientType.WEIXIN && !sysConfigService.getBoolean(
                "bool-wxxcx-menzhen-tuikuan",
                true
            )
        ) {
            return AjaxResult.error("挂号退款功能正在升级维护，暂时使用，敬请谅解")
        } else {
            if (!sysConfigService.getBoolean("bool-zfbxcx-menzhen-tuikuan", true)) {
                return AjaxResult.error("挂号退款功能正在升级维护，暂停使用，敬请谅解")
            }
        }

        val reservation =
            reservationService.getOneBySubscriptionId(subscriptionId) ?: return AjaxResult.error("仅支持取消本小程序产生的预约订单")
        if (reservation.patientId != currentPatient.patientNo) {
            return AjaxResult.error("当前用户无权操作此订单")
        }
        if (reservation.paymentType == PAYMENT_TYPE_WECHAT_MIP) {
            var paidByMip = true
            val mipWxOrder = mipWxOrderService.getById(reservation.paymentId ?: 0L)
            if (mipWxOrder == null) {
                // 找不到关联的医保订单
                // 检查患者是否通过诊间支付模块，使用微信支付缴费
                val wxPayment = wxPaymentService.selectOneByBusinessSerialNumber(reservation.prescriptionNo)
                if (wxPayment == null) {
                    return AjaxResult.error("未查询到关联的支付订单")
                } else {
                    paidByMip = false
                }
            }

            if (paidByMip) {
                val mipAccountType =
                    if (currentPatient.openId == reservation.openid) {
                        if (mipWxOrder.isSelfPay) {
                            "self"
                        } else {
                            "family"
                        }
                    } else {
                        "self"
                    }
                return AjaxResult.error("MIP_REFUND_REQUIRED", mapOf("mipAccountType" to mipAccountType))
            }
        }

        val refundNo = newRefundNo(ServiceType.GH, subscriptionId)
        val cancelSubscriptionRequest = CancelSubscriptionRequest().apply {
            this.subscriptionId = subscriptionId
            this.tradeNo = refundNo
            this.tradeDate = DateUtil.formatNow()
        }
        val cancelReservationResult: CancelSubscriptionResult = service.execute(
            request = cancelSubscriptionRequest
        )

        return if (currentPatient.clientTypeEnum == ClientType.WEIXIN) {
            wxRefund(subscriptionId, refundNo, currentPatient, reservation, cancelReservationResult)
        } else {
            aliRefund(subscriptionId, refundNo, currentPatient, reservation, cancelReservationResult)
        }

    }

    @PostMapping("refundMip")
    @RequireActivePatient
    fun refundMip(
        @RequestParam subscriptionId: String,
        @RequestParam mipAuthCode: String
    ): AjaxResult {
        val currentPatient = request.getCurrentPatient()

        val reservation =
            reservationService.getOneBySubscriptionId(subscriptionId) ?: return AjaxResult.error("未查询到预约订单")
        if (reservation.patientId != currentPatient.patientNo) {
            return AjaxResult.error("当前就诊人不能操作此订单")
        }
        if (reservation.paymentType != PAYMENT_TYPE_WECHAT_MIP) {
            return AjaxResult.error("该订单不支持当前操作")
        }
        if (reservation.paymentId == null) {
            return AjaxResult.error("未查询到关联的医保订单")
        }

        val userQueryResult = try {
            WecityMipService.queryUser(
                qrcode = mipAuthCode,
                openid = currentPatient.openId
            )
        } catch (e: Exception) {
            traceService.traceReservation(reservation.id, "查询用户医保信息失败：${e.stackTraceToString()}")
            logger.error(e.message, e)
            return AjaxResult.error("查询用户医保信息失败")
        }

        if (!userQueryResult.isOK()) {
            traceService.traceReservation(
                reservation.id,
                "查询用户医保信息失败：${JacksonKit.writeValueAsString(userQueryResult)}"
            )
        }

        if (userQueryResult.cityId.isNullOrBlank()) {
            traceService.traceReservation(
                reservation.id,
                "查询用户参保地失败：${JacksonKit.writeValueAsString(userQueryResult)}"
            )
            return AjaxResult.error("获取医保参保地失败")
        }
        if (!userQueryResult.cityId!!.startsWith("65")) {
            traceService.traceReservation(
                reservation.id,
                "目前仅支持新疆维吾尔自治区医保：${JacksonKit.writeValueAsString(userQueryResult)}"
            )
            return AjaxResult.error("目前仅支持新疆维吾尔自治区医保")
        }
        if (userQueryResult.isSelfPay() && (userQueryResult.userCardNo != currentPatient.idCardNo || userQueryResult.userName != currentPatient.name)) {
            traceService.traceReservation(
                reservation.id,
                "医保信息与当前就诊人信息不一致：医保信息（${userQueryResult.userName}, ${userQueryResult.userCardNo}）vs. 当前就诊人信息（${currentPatient.name}, ${currentPatient.idCardNo}"
            )
            return AjaxResult.error("请在医保电子凭证服务授权页，选择【${currentPatient.name}】的医保账户")
        }

        val payAuthNo = if (userQueryResult.isSelfPay()) {
            userQueryResult.payAuthNo!!
        } else if (userQueryResult.isFamilyPay()) {
            userQueryResult.familyPayAuthNo!!
        } else {
            // Unreachable
            return AjaxResult.error("未知的医保支付方式")
        }

        val mipWxOrder =
            mipWxOrderService.getById(reservation.paymentId) ?: return AjaxResult.error("未查询到关联的医保订单")
        val mipRefundRequest = MipRefundRequest(
            clinicNo = mipWxOrder.clinicNo,
            payOrdId = mipWxOrder.payOrderId,
            payAuthNo = payAuthNo,
            ecToken = "",
            psnName = mipWxOrder.patientName,
            psnIdenNo = mipWxOrder.patientIdCardNo,
            insuCode = mipWxOrder.cityId,
            extParam = ""
        )
        val mipRefundResponse =
            NeusoftMipPayService.mipRefund(request = HisMipBaseRequest(mipRefundRequest))
        return if (mipRefundResponse.isOk) {
            reservation.operationDesc = "取消预约成功"
            reservation.status = RESERVATION_STATUS_CANCELED
            reservation.paymentStatus = PAYMENT_STATUS_REFUND
            reservationService.updateById(reservation)
            traceService.traceReservation(reservation.id, "退号退医保成功")
            AjaxResult.success()
        } else {
            traceService.traceReservation(
                reservation.id,
                "退号退医保失败：${JacksonKit.writeValueAsString(mipRefundResponse)}"
            )
            AjaxResult.error(mipRefundResponse.message)
        }
    }

    fun aliRefund(
        subscriptionId: String,
        refundNo: String,
        activePatient: Patient,
        reservation: Reservation,
        cancelReservationResult: CancelSubscriptionResult
    ): AjaxResult {


        synchronized(subscriptionId.intern()) {
            val payment = alipayPaymentService.selectAlipayPaymentList(AlipayPayment().apply {
                this.hisReceiptNo = subscriptionId
                this.type = ServiceType.GH.code
            }).firstOrNull() ?: return AjaxResult.error("找不到指定的充值订单")

            if (payment.patientId != activePatient.patientNo) {
                return AjaxResult.error("当前就诊卡不能操作此订单")
            }
            if (payment.type != ServiceType.MZ.code) {
                return AjaxResult.error("此订单不是门诊订单")
            }
            if (payment.hisTradeStatus != Constants.RECHARGE_OK) {
                return AjaxResult.error("此订单不支持退款")
            }

            val refund = AlipayRefund(payment, payment.totalAmount, cancelReservationResult)
            refund.outRefundNo = refundNo
            alipayRefundService.insertAlipayRefund(refund)

            if (cancelReservationResult.isOk) {
                val cancelReservation: Reservation = reservation.toCancel()
                cancelReservation.operationDesc = "取消预约成功"
                reservationService.save(cancelReservation)

                payment.exrefund = payment.totalAmount
                payment.unrefund = 0
                alipayPaymentService.updateAlipayPayment(payment)
                alipayRefundService.refundAlipay(refund)
                reservationService.pushAlipayHospitalOrder(
                    cancelReservation,
                    HospitalOrderStatus.MERCHANT_CLOSED,
                    reservation.operationTime,
                    cancelReservation.operationTime,
                    reservation.id
                )
                return AjaxResult.success()
            } else {
                return AjaxResult.error(cancelReservationResult.message)
            }
        }
    }

    /**
     * 退款
     * @param subscriptionId 掌医充值订单号
     * @param amount 退款金额，以分为单位
     */
    fun wxRefund(
        subscriptionId: String,
        refundNo: String,
        activePatient: Patient,
        reservation: Reservation,
        cancelReservationResult: CancelSubscriptionResult
    ): AjaxResult {
        synchronized(subscriptionId.intern()) {
//            if (wxPayment.type != ServiceType.GH.name) {
//                return AjaxResult.error("此订单不是门诊订单")
//            }

//            if (wxPayment.hisTradeStatus != Constants.RECHARGE_OK) {
//                return AjaxResult.error("此订单不支持退款")
//            }
//
//            if (wxPayment.amount > 0 && wxPayment.unrefund <= 0) {
//                return AjaxResult.error("退款金额超出订单限制")
//            }

//            if (wxPayment.amount > wxPaymentService.calculateNetInAmount(LocalDate.now())) {
//                return AjaxResult.error("商户余额不足，建议您两小时后重试")
//            }

            return if (cancelReservationResult.isOk) {
                val cancelReservation: Reservation = reservation.toCancel()
                cancelReservation.operationDesc = "取消预约成功"
                reservationService.save(cancelReservation)

                reservation.operationDesc = "取消预约成功"
                reservation.status = RESERVATION_STATUS_CANCELED
                reservationService.updateById(reservation)

                if (NumberUtil.equals(reservation.clinicFee, BigDecimal.ZERO)) {
                    return AjaxResult.success()
                }

                val wxPayment =
                    wxPaymentService.selectWxPaymentList(WxPayment().apply {
                        this.hisReceiptNo = subscriptionId
                        this.type = ServiceType.GH.description
                    }).firstOrNull()
                        ?: wxPaymentService.selectOneByBusinessSerialNumber(reservation.id.toString())
                        ?: wxPaymentService.selectOneByBusinessSerialNumber(reservation.prescriptionNo)
                        ?: return AjaxResult.error("找不到指定的充值订单")

                if (wxPayment.jzCardNo != activePatient.jzCardNo) {
                    return AjaxResult.error("当前就诊卡不能操作此订单")
                }

                val refundRequest: WxPayRefundRequest =
                    WxPayRefundRequest.newBuilder()
                        .transactionId(wxPayment.wxPayNo)
                        .outRefundNo(refundNo.ifBlank { newRefundNo(ServiceType.GH, reservation.subscriptionId) })
                        .totalFee(wxPayment.amount.toInt())
                        .refundFee(wxPayment.amount.toInt())
                        .notifyUrl(serverConfig.onWxRefundUrl)
                        .build()

                val wxRefund: WxRefund =
                    wxRefundService.createAndSave(wxPayment, refundRequest, cancelReservationResult)
                        ?: return AjaxResult.error("创建退款订单失败")
                // 更新已退金额、可退金额
                wxPaymentService.updateOnRefund(wxPayment, wxPayment.amount)

                try {
                    var result = WxPayRefundResult()
                    if (wxPayment.amount != 0L) {
                        result = WeixinExt.refund(
                            totalAmount = wxPayment.amount.toInt(),
                            amount = wxRefund.amount.toInt(),
                            wxPayNo = wxRefund.wxPayNo,
                            zyRefundNo = wxRefund.zyRefundNo
                        )
                    }
                    CoroutineScope(Dispatchers.IO).launch {
                        WeixinExt.sendSubscribeMessage(
                            messageId = subscribeMessageConfiguration.cancelYuyueId,
                            messagePage = subscribeMessageConfiguration.cancelYuyuePage,
                            messageDataList = listOf(
                                WeixinExt.newWxMaSubscribeMessagePhraseData("phrase2", activePatient.name),
                                WeixinExt.newWxMaSubscribeMessagePhraseData("phrase5", cancelReservation.doctorName),
                                WeixinExt.newWxMaSubscribeMessageThingData("thing4", cancelReservation.departmentName),
                                WeixinExt.newWxMaSubscribeMessageThingData(
                                    "thing12",
                                    DatePattern.NORM_DATETIME_FORMAT.format(cancelReservation.jzTime)
                                ),
                                WeixinExt.newWxMaSubscribeMessageThingData("thing8", "取消成功"),
                            ),
                            openid = activePatient.openId,
                            companionId = "ph_yuyue_history#${cancelReservation.id}"
                        )
                    }
                    AjaxResult.success(result)
                } catch (e: Exception) {
                    logger.debug(e.message, e)
                    logger.error(
                        """
                            ***** 微信挂号退款失败 *****
                            下单时间：【${DateUtil.formatDateTime(wxRefund.createTime)}】
                            就诊卡号：【${wxRefund.jzCardNo}】
                            掌医退款单号：【${wxRefund.zyRefundNo}】
                            订单金额：【${PaymentKit.fenToYuan(wxRefund.amount)}】
                            错误信息：【${e.message}】   
                        """.trimIndent()
                    )
                    AjaxResult.error(e.message)
                }
            } else {
                AjaxResult.error("退号失败:${cancelReservationResult.message}")
            }
        }
    }

}
