package space.lzhq.ph.controller

import com.alibaba.fastjson.JSONObject
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.system.service.ISysConfigService
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.runBlocking
import org.dromara.hutool.core.date.DatePattern
import org.dromara.hutool.core.date.DateUtil
import org.mospital.neusoft.request.QueryRealtimeScheduleDetailRequest
import org.mospital.neusoft.request.QueryScheduleRequest
import org.mospital.neusoft.request.QueryScheduleTimeRequest
import org.mospital.neusoft.result.FindAllDepartmentsResult
import org.mospital.neusoft.util.NeusoftService
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import space.lzhq.ph.annotation.RequireActivePatient
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.common.Values
import space.lzhq.ph.domain.Doctor
import space.lzhq.ph.domain.Patient
import space.lzhq.ph.ext.HisExt
import space.lzhq.ph.ext.getCurrentPatient
import space.lzhq.ph.model.DoctorWrapper
import space.lzhq.ph.service.DepartmentKit
import space.lzhq.ph.service.IDoctorService
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneId
import java.util.*

@RestController
@RequestMapping("/open/schedule")
class ScheduleApiController(
    private val doctorService: IDoctorService,
    private val sysConfigService: ISysConfigService,
) : BaseController() {

    @Suppress("UNCHECKED_CAST")
    @GetMapping("indexedDepartments")
    fun indexedDepartments(): AjaxResult {
        if (Values.INDEXED_DEPARTMENTS.isEmpty()) {
            DepartmentKit.sync()
        }
        val start = Date()
        val request = QueryScheduleRequest().apply {
            startDate = DatePattern.NORM_DATE_FORMAT.format(start)
            endDate = DatePattern.NORM_DATE_FORMAT.format(DateUtil.offsetDay(start, 8))
        }
        val execute = NeusoftService.me.execute(request)
        if (execute.isFail) {
            return AjaxResult.error(execute.message)
        }
        val departments = execute.content.map { it.departmentCode }.toSet()

        val newDepartments = mutableListOf<Map<String, Any>>()
        Values.INDEXED_DEPARTMENTS.forEach {
            val newDepts: List<FindAllDepartmentsResult.Department> =
                (it["departments"] as List<FindAllDepartmentsResult.Department>).filter { dept ->
                    departments.contains(
                        dept.departmentCode
                    )
                }
            if (newDepts.isNotEmpty()) {
                newDepartments.add(
                    mapOf(
                        "index" to it["index"]!!,
                        "departments" to newDepts
                    )
                )
            }
        }
        return AjaxResult.success(newDepartments)
    }

    /**
     * 掌医本地医生列表，可适用于按医生分类查询
     */
    @GetMapping("doctorList")
    fun doctorList(doctor: DoctorWrapper): AjaxResult {
        startPage("sort_no desc")
        val list: List<Doctor> = doctorService.selectDoctorList(doctor)
        return AjaxResult.success(list)
    }

    /**
     * 查询排班医生
     * @param departmentCode 科室代码
     * @param date 排班日期
     */
    @GetMapping("doctors")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    @RequireSession
    fun doctors(
        @RequestParam departmentCode: String,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) date: LocalDate,
    ): AjaxResult {
        val currentPatient: Patient = request.getCurrentPatient()
        val requestStr = QueryRealtimeScheduleDetailRequest().apply {
            this.clinicDate = date.toString()
            this.departmentCode = departmentCode
        }
        val doctorsResult = NeusoftService.me.execute(requestStr)
        if (doctorsResult.isFail) return AjaxResult.error("查询排班医生失败")

        val doctors = doctorsResult.content
            .filter { it.status.isBlank() || it.status == "1" }
            .map {
                //普通挂号费是6元，需要根据大于这个值的为急诊门诊
                it.clinicType =
                    if (it.clinicType == "普通门诊" && it.clinicFee.toBigDecimal() >= sysConfigService.yuyueType.ifBlank { "12" }
                            .toBigDecimal()) {
                        "急诊门诊"
                    } else {
                        it.clinicType
                    }
                //如果超过当天号源时间的，将剩余号源数置为0
                val endtime = "$date ${it.clinicTimeQuantum.ifBlank { "0-23:59" }.split("-")[1]}"
                if (DateUtil.compare(Date(), DatePattern.NORM_DATETIME_MINUTE_FORMAT.parse(endtime)) > 0) {
                    it.clinicNo = "0"
                }

                //添加超过65周岁的折扣
                it.clinicFee = HisExt.getClinicFee(currentPatient, it.clinicFee, it.underRegFee, it.overRegFee)
                it.realRegFee = it.clinicFee

                val jsonObject = JSONObject.toJSON(it) as JSONObject
                jsonObject["doctorDetail"] = null

                val doctorWrapper = DoctorWrapper().apply {
                    this.id = it.doctorCode
                }
                val doctorDetail = doctorService.selectDoctorList(doctorWrapper).firstOrNull() ?: return@map jsonObject
                jsonObject["doctorDetail"] = doctorDetail

                jsonObject
            }
        return AjaxResult.success(doctors)
    }

    /**
     * 查询一段时间排班医生
     */
    @GetMapping("queryDoctorByDays")
    fun queryDoctorByDays(
        @RequestParam(required = false) departmentCode: String?,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) date: LocalDate,
        @RequestParam(defaultValue = "1") days: Long
    ): AjaxResult {
        val dateRange = (0 until days).map { date.plusDays(it) }

        val doctorsResult = runBlocking {
            dateRange
                .map { clinicDate ->
                    async {
                        val request = QueryRealtimeScheduleDetailRequest().apply {
                            this.clinicDate = clinicDate.toString()
                            this.departmentCode = departmentCode
                        }
                        NeusoftService.me.execute(request)
                    }
                }.toList()
                .awaitAll()
                .mapIndexed { index, result ->
                    dateRange[index] to result.content
                }
                .toMap()
        }
        return AjaxResult.success(doctorsResult)
    }

    /**
     * 查询排班号源
     * @param departmentCode 科室代码
     * @param doctorCode 医生代码
     * @param date 排班日期
     * @param ampm 上午下午，参见 period.code ：1=上午，2=下午，3=晚上
     */
    @GetMapping("tickets")
    fun tickets(
        @RequestParam departmentCode: String,
        @RequestParam doctorCode: String,
        @RequestParam("clinicDate") date: Date,
        @RequestParam ampm: String,
        @RequestParam scheduleId: String,
    ): AjaxResult {
        val request = QueryScheduleTimeRequest().apply {
            this.departmentCode = departmentCode
            this.clinicDate = DatePattern.NORM_DATE_FORMAT.format(date)
            this.clinicTime = ampm
            this.doctorCode = doctorCode
            this.scheduleId = scheduleId
        }
        val result = NeusoftService.me.execute(request)

        return if (result.isOk) {
            AjaxResult.success(result.content
                .sortedBy { LocalTime.parse(it.visitTime) }
                .filter {
                    val clinicDate = DateUtil.parse(it.clinicDate as CharSequence)
                    val clinicTime = LocalTime.parse(it.visitTime)
                    val clinicDateTime = LocalDateTime.of(
                        clinicDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
                        clinicTime
                    )
                    clinicDateTime > LocalDateTime.now()
                })
        } else {
            AjaxResult.error(result.message)
        }
    }

    /**
     * 全文搜索医生
     */
    @GetMapping("searchDoctor")
    fun searchDoctor(
        @RequestParam keywords: String,
        @RequestParam(defaultValue = "10") limit: Int
    ): AjaxResult {
        if (keywords.length < 2) return AjaxResult.error("关键词长度至少两个字")

        val doctorList = doctorService.fulltextSearch(keywords, limit)
        return AjaxResult.success(doctorList)
    }
}