package space.lzhq.ph.job

import com.github.binarywang.wxpay.service.WxPayService
import org.dromara.hutool.core.date.DatePattern
import org.dromara.hutool.core.date.DateTime
import org.dromara.hutool.core.date.DateUtil
import org.mospital.alipay.AlipayService.queryOrder
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import space.lzhq.ph.domain.AlipayPayment
import space.lzhq.ph.domain.WxPayment
import space.lzhq.ph.ext.HisExt
import space.lzhq.ph.service.IAlipayPaymentService
import space.lzhq.ph.service.IReservationService
import space.lzhq.ph.service.IWxPaymentService

/**
 * 自动充值
 * 表达式：0 0/2 * * * ?
 * <AUTHOR>
 */
@Component("autoRechargeJob")
class AutoRechargeJob {

    @Autowired
    private lateinit var wxPaymentService: IWxPaymentService

    @Autowired
    private lateinit var alipayPaymentService: IAlipayPaymentService

    @Autowired
    private lateinit var reservationService: IReservationService

    @Autowired
    private lateinit var wxPayService: WxPayService

    fun execute() {
        val timeRange = buildTimeRange()

        refreshOrderStatusForWxPayments(timeRange)
        rechargeForWxPayments(timeRange)

        refreshOrderStatusForAliPayments(timeRange)
        rechargeForAliPayments(timeRange)
    }

    private fun buildTimeRange(): Array<String> {
        val now = DateTime.now()
        val beginCreateTime = DateUtil.offsetMinute(now, -10).toString(DatePattern.NORM_DATETIME_FORMAT)
        val endCreateTime = DateUtil.offsetMinute(now, -2).toString(DatePattern.NORM_DATETIME_FORMAT)
        return arrayOf(beginCreateTime, endCreateTime)
    }

    private fun refreshOrderStatusForWxPayments(timeRange: Array<String>) {
        val params = mutableMapOf<String, Any>(
            "beginCreateTime" to timeRange[0],
            "endCreateTime" to timeRange[1],
            "wxTradeStatusIsUnknown" to true
        )
        val payments = wxPaymentService.selectWxPaymentList(WxPayment().apply {
            this.params = params
        })
        payments.forEach {
            val queryOrderResponse = wxPayService.queryOrder(null, it.zyPayNo)
            wxPaymentService.updateOnPay(it, queryOrderResponse)
        }
    }

    private fun rechargeForWxPayments(timeRange: Array<String>) {
        val params = mutableMapOf<String, Any>(
            "beginCreateTime" to timeRange[0],
            "endCreateTime" to timeRange[1],
            "filterErrorOrder" to true
        )

        val payments = wxPaymentService.selectWxPaymentList(WxPayment().apply {
            this.params = params
        })
        payments.forEach {
            HisExt.recharge(it.id)
        }
    }

    private fun refreshOrderStatusForAliPayments(timeRange: Array<String>) {
        val params = mutableMapOf<String, Any>(
            "beginCreateTime" to timeRange[0],
            "endCreateTime" to timeRange[1],
            "tradeStatusIsUnknown" to true
        )
        val payments = alipayPaymentService.selectAlipayPaymentList(AlipayPayment().apply {
            this.params = params
        })
        payments.forEach {
            val queryOrderResponse = queryOrder(
                outTradeNo = it.outTradeNo,
                tradeNo = "",
                queryOptions = listOf("fund_bill_list")
            )
            alipayPaymentService.updateOnPay(it, queryOrderResponse)
        }
    }

    private fun rechargeForAliPayments(timeRange: Array<String>) {
        val params = mutableMapOf<String, Any>(
            "beginCreateTime" to timeRange[0],
            "endCreateTime" to timeRange[1],
            "filterErrorOrder" to true
        )

        val payments = alipayPaymentService.selectAlipayPaymentList(AlipayPayment().apply {
            this.params = params
        })
        payments.forEach {
            HisExt.rechargeByAlipay(it.id)
        }
    }

}
