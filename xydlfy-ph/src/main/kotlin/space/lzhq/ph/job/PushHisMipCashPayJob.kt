package space.lzhq.ph.job

import cn.doit.zhangyi.neusoft.bean.json.MipSettleCallBackRequest
import cn.doit.zhangyi.neusoft.bean.json.MipSettleStatusQueryRequest
import org.dromara.hutool.core.date.DatePattern
import org.dromara.hutool.core.date.DateTime
import org.dromara.hutool.core.date.DateUtil
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper
import com.ruoyi.common.constant.Constants
import org.mospital.jackson.JacksonKit
import org.mospital.neusoft.mip.HisMipBaseRequest
import org.mospital.neusoft.service.NeusoftMipPayService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import space.lzhq.ph.domain.MipWxOrder
import space.lzhq.ph.domain.Reservation
import space.lzhq.ph.ext.MipHisStatusEnum
import space.lzhq.ph.service.IMipWxOrderService
import space.lzhq.ph.service.IReservationService
import space.lzhq.ph.service.ITraceService
import java.util.*

/**
 * 自动充值
 * 表达式：0 0/2 * * * ?
 * <AUTHOR>
 */
@Component("pushHisMipCashPayJob")
class PushHisMipCashPayJob {
    @Autowired
    private lateinit var mipWxOrderService: IMipWxOrderService

    @Autowired
    private lateinit var reservationService: IReservationService

    @Autowired
    private lateinit var traceService: ITraceService

    private val logger = LoggerFactory.getLogger(this.javaClass)


    fun execute() {
        //查询近1小时内微信医保支付成功，his未推送结算的数据，发起HIS结算
        val timeRange = buildTimeRange()
        val pushList = LambdaQueryChainWrapper(mipWxOrderService.baseMapper)
            .eq(MipWxOrder::getCashPayStatus, Constants.PAY_OK)
            .and { it.eq(MipWxOrder::getHisSettleStatus, "").or().eq(MipWxOrder::getHisSettleStatus, "fail") }
//            .and { it.isNull(MipWxOrder::getHisSettleStatus).or().ge(MipWxOrder::getCashPayStatus, "") }
            .gt(MipWxOrder::getCashPayTime, timeRange[0])
            .le(MipWxOrder::getCashPayTime, timeRange[1])

            .list()

        pushList.forEach {
            try {
                singleExecute(it)
            } catch (e: Exception) {
                logger.error("移动医保院内结算失败:${it.payOrderId} " + e.message)
            }
        }

    }

    private fun buildTimeRange(): Array<String> {
        val now = DateTime.now()
        val beginCreateTime = DateUtil.offsetMinute(now, -30).toString(DatePattern.NORM_DATETIME_FORMAT)
        val endCreateTime = DateUtil.offsetSecond(now, -30).toString(DatePattern.NORM_DATETIME_FORMAT)
        return arrayOf(beginCreateTime, endCreateTime)
    }
    
    private fun singleExecute(mipWxOrder: MipWxOrder) {
        synchronized(mipWxOrder.medTransactionId.intern()) {
            val reservation = reservationService.lambdaQuery()
                .eq(Reservation::getPaymentId, mipWxOrder.id)
                .eq(Reservation::getOperationType, Reservation.OPERATION_TYPE_RESERVE)
                .one()

            val hisStatus = NeusoftMipPayService.settleStatusQuery(
                HisMipBaseRequest(
                    MipSettleStatusQueryRequest(
                        clinicNo = mipWxOrder.clinicNo,
                        payOrdId = mipWxOrder.payOrderId,
                        psnName = mipWxOrder.patientName,
                        psnIdenNo = mipWxOrder.patientIdCardNo,
                    )
                )
            )
            //查询HIS订单确认订单状态，如果
            if (hisStatus.isOk && hisStatus.data?.ordStas == MipHisStatusEnum.SETTLEMENT_COMPLETED.code) {

                if (reservation != null && reservation.status in arrayOf(Reservation.RESERVATION_STATUS_RELEASED, Reservation.RESERVATION_STATUS_USED)) {
                    traceService.traceReservation(reservation.id, "预约状态为 ${reservation.status}，不再执行挂号费结算")
                    return
                }

                //回调HIS，进行院内结算
                val settleCallBackRequest =
                    JacksonKit.readValue(mipWxOrder.settleCallbackRequest, MipSettleCallBackRequest::class.java)

                val callBackResult = NeusoftMipPayService.settleCallBack(
                    request = HisMipBaseRequest(settleCallBackRequest)
                )
                if (callBackResult.isOk) {
                    logger.info("移动医保院内结算成功:${mipWxOrder.payOrderId} " + callBackResult.message)
                    mipWxOrder.settleCallbackResult = JacksonKit.writeValueAsString(callBackResult.data)

                    mipWxOrder.hisSettleTime = try {
                        DateUtil.parse(callBackResult.data!!.hisSetlTime)
                    } catch (e: Exception) {
                        Date()
                    }
                    mipWxOrder.hisSettleStatus = "SUCCESS"

                    if (reservation != null) {
                        reservation.paymentStatus = Reservation.PAYMENT_STATUS_PAID
                        reservationService.lambdaUpdate()
                            .set(Reservation::getPaymentStatus, Reservation.PAYMENT_STATUS_PAID)
                            .eq(Reservation::getId, reservation.id)
                            .update()
                        traceService.traceReservation(reservation.id, "挂号费结算（M002）成功: ${JacksonKit.writeValueAsString(callBackResult)}")
                    }
                } else {
                    logger.error("移动医保院内结算失败:${mipWxOrder.payOrderId} " + callBackResult.message)
                    mipWxOrder.hisSettleStatus = "fail"

                    if (reservation != null) {
                        traceService.traceReservation(reservation.id, "挂号费结算（M002）失败: ${JacksonKit.writeValueAsString(callBackResult)}")
                    }
                }
                mipWxOrderService.updateById(mipWxOrder)
            }
        }
    }

}
