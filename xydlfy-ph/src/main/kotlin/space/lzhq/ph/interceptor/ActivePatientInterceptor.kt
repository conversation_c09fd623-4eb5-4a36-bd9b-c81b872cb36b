package space.lzhq.ph.interceptor

import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.json.JSON
import com.ruoyi.common.utils.ServletUtils
import com.ruoyi.common.utils.spring.SpringUtils
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.slf4j.MDC
import org.springframework.stereotype.Component
import org.springframework.web.method.HandlerMethod
import org.springframework.web.servlet.HandlerInterceptor
import space.lzhq.ph.annotation.RequireActivePatient
import space.lzhq.ph.common.Values.CURRENT_PATIENT
import space.lzhq.ph.domain.Patient
import space.lzhq.ph.domain.Session
import space.lzhq.ph.ext.getClientSession
import space.lzhq.ph.service.IPatientService

@Component
class ActivePatientInterceptor : HandlerInterceptor {

    override fun preHandle(request: HttpServletRequest, response: HttpServletResponse, handler: Any): Bo<PERSON>an {
        if (handler !is HandlerMethod) {
            return super.preHandle(request, response, handler)
        }

        val requireActivePatient = handler.method.getAnnotation(RequireActivePatient::class.java) ?: return true

        val session: Session? = request.getClientSession()
        if (session == null) {
            ServletUtils.renderString(response, JSON.marshal(AjaxResult.error("会话无效，请重新登录")))
            return false
        }

        val patientService = SpringUtils.getBean(IPatientService::class.java)
        val currentPatient: Patient? = patientService.selectActivePatientByOpenId(session.openId)
        if (currentPatient == null) {
            ServletUtils.renderString(response, JSON.marshal(AjaxResult.error("获取默认就诊人失败")))
            return false
        }

        MDC.put("patientId", currentPatient.patientNo)

        if (requireActivePatient.shouldBeMenzhenPatient && currentPatient.jzCardNo.isNullOrBlank()) {
            ServletUtils.renderString(response, JSON.marshal(AjaxResult.error("请绑定就诊卡号")))
            return false
        }

        if (requireActivePatient.shouldBeZhuyuanPatient && currentPatient.zhuyuanNo.isNullOrBlank()) {
            ServletUtils.renderString(response, JSON.marshal(AjaxResult.error("请绑定住院号")))
            return false
        }

        request.setAttribute(CURRENT_PATIENT, currentPatient)
        return true
    }

    override fun afterCompletion(
        request: HttpServletRequest,
        response: HttpServletResponse,
        handler: Any,
        ex: Exception?
    ) {
        MDC.remove("patientId")
    }

}
