package space.lzhq.ph.ext

enum class MipHisStatusEnum(val code: String, val description: String) {
    SAVED("0", "已保存"),
    PRE_SETTLEMENT("1", "预结算完成"),
    SETTLEMENT_IN_PROGRESS("2", "结算中"),
    SELF_PAY_COMPLETED("3", "自费完成"),
    MEDICAL_PAY_COMPLETED("4", "医保支付完成"),
    HOSPITAL_SETTLEMENT_COMPLETED("5", "院内结算完成"),
    SETTLEMENT_COMPLETED("6", "结算完成"),
    REFUNDED("7", "已退款"),
    MEDICAL_REFUND_COMPLETED("8", "已医保全部退款"),
    SELF_PAY_REFUND_FULL("9", "仅自费全部退款"),
    SELF_PAY_REFUND_PARTIAL("10", "仅自费部分退款"),
    MEDICAL_PAY_REFUND("11", "医保全部退自费部分退款"),
    CANCELLED("12", "已撤销"),
    MEDICAL_CANCELLED("13", "医保已撤销"),
    EXCEPTION("14", "异常"),
    SETTLEMENT_FAILED("15", "结算失败"),
    MEDICAL_SETTLEMENT_FAILED("16", "医保结算失败自费冲正失败"),
    NO_MEDICAL_INFO("-1", "医保接口基线版“个人结算信息查询5203”，没有查到该笔订单的医保个人结算信息（已冲正）"),
    MEDICAL_REFUNDED("-2", "医保接口基线版“个人结算信息查询5203”，查到该笔订单的医保结算已做退费");

    companion object {
        fun fromCode(code: String): MipHisStatusEnum? {
            return values().find { it.code == code }
        }
    }
}
