package space.lzhq.ph.ext

import cn.binarywang.wx.miniapp.api.WxMaService
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage
import org.dromara.hutool.core.date.DatePattern
import org.dromara.hutool.core.date.DateTime
import org.dromara.hutool.core.date.DateUtil
import org.dromara.hutool.core.io.file.FileUtil
import org.dromara.hutool.core.text.StrUtil
import org.dromara.hutool.http.HttpUtil
import org.dromara.hutool.log.LogFactory
import com.alibaba.fastjson.JSON
import com.github.binarywang.wxpay.bean.order.WxPayMpOrderResult
import com.github.binarywang.wxpay.bean.request.WxInsurancePayDownloadBillRequest
import com.github.binarywang.wxpay.bean.request.WxPayDownloadBillRequest
import com.github.binarywang.wxpay.bean.request.WxPayRefundRequest
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest
import com.github.binarywang.wxpay.bean.result.WxPayBillInfo
import com.github.binarywang.wxpay.bean.result.WxPayBillResult
import com.github.binarywang.wxpay.bean.result.WxPayRefundResult
import com.github.binarywang.wxpay.config.WxPayConfig
import com.github.binarywang.wxpay.constant.WxPayConstants
import com.github.binarywang.wxpay.exception.WxPayException
import com.github.binarywang.wxpay.service.WxInsurancePayService
import com.github.binarywang.wxpay.service.WxPayService
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl
import com.ruoyi.common.config.ServerConfig
import com.ruoyi.common.constant.Constants
import com.ruoyi.common.utils.spring.SpringUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.launch
import me.chanjar.weixin.common.bean.ocr.WxOcrIdCardResult
import me.chanjar.weixin.common.service.WxOcrService
import org.dromara.hutool.http.client.HttpDownloader
import org.mospital.jackson.JacksonKit
import org.mospital.wecity.mip.WeixinMipSetting
import space.lzhq.ph.common.PaymentKit
import space.lzhq.ph.common.ServiceType
import space.lzhq.ph.common.StringKit
import space.lzhq.ph.configuration.WxSubscribeMessageConfiguration
import space.lzhq.ph.domain.Patient
import space.lzhq.ph.domain.WxSubscribeMessage
import space.lzhq.ph.domain.ZhuyuanPatient
import space.lzhq.ph.service.IWxSubscribeMessageService
import java.io.File
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.*

object WeixinExt {

    private val log = LogFactory.getLog(WeixinExt::class.java)

    fun createUnifiedOrderRequest(
        serviceType: ServiceType,
        openid: String,
        amount: Int,
        ip: String,
        remark: String = "",
        expireTime: LocalDateTime = LocalDateTime.now().plusMinutes(10)
    ): WxPayUnifiedOrderRequest {
        val now: Date = DateTime.now()
        val startTimeString: String = DateUtil.format(now, DatePattern.PURE_DATETIME_PATTERN)
        // 10分钟后过期
        val expireTimeString: String = expireTime.format(DatePattern.PURE_DATETIME_FORMATTER)

        val bodyString: String = "${Constants.MERCHANT_NAME}-" + when (serviceType) {
            ServiceType.MZ -> serviceType.description
            ServiceType.ZY -> "住院预交金"
            ServiceType.ZJ -> "诊间缴费"
            ServiceType.GH -> "挂号"
            else -> ""
        } + (if (remark.isNotBlank()) "-$remark" else "")

        val serverConfig: ServerConfig = SpringUtils.getBean(ServerConfig::class.java)
        return WxPayUnifiedOrderRequest.newBuilder()
            .tradeType(WxPayConstants.TradeType.JSAPI)
            .body(bodyString)
            .outTradeNo(PaymentKit.newOrderId(serviceType))
            .totalFee(amount)
            .spbillCreateIp(ip)
            .timeStart(startTimeString)
            .timeExpire(expireTimeString)
            .notifyUrl(serverConfig.onWxPayUrl)
            .openid(openid)
            .build()
    }

    fun pay(
        serviceType: ServiceType,
        amount: Int,
        ip: String,
        patient: Patient,
        expireTime: LocalDateTime = LocalDateTime.now().plusMinutes(10),
        remark: String = "",
        callback: (WxPayUnifiedOrderRequest, WxPayMpOrderResult) -> Unit,
    ): WxPayMpOrderResult {
        val unifiedOrderRequest: WxPayUnifiedOrderRequest = createUnifiedOrderRequest(
            serviceType = serviceType,
            openid = patient.openId,
            amount = amount,
            ip = ip,
            expireTime = expireTime,
            remark = remark
        )
        val wxPayService = SpringUtils.getBean(WxPayService::class.java)
        val orderResult: WxPayMpOrderResult = wxPayService.createOrder(unifiedOrderRequest)

        CoroutineScope(Dispatchers.IO).launch {
            callback(unifiedOrderRequest, orderResult)
        }

        return orderResult
    }

    fun pay(
        serviceType: ServiceType,
        amount: Int,
        ip: String,
        patient: ZhuyuanPatient,
        expireTime: LocalDateTime = LocalDateTime.now().plusMinutes(10),
        remark: String = "",
        callback: (WxPayUnifiedOrderRequest, WxPayMpOrderResult) -> Unit,
    ): WxPayMpOrderResult {
        val unifiedOrderRequest: WxPayUnifiedOrderRequest = createUnifiedOrderRequest(
            serviceType = serviceType,
            openid = patient.openId,
            amount = amount,
            ip = ip,
            expireTime = expireTime,
            remark = remark
        )
        val wxPayService = SpringUtils.getBean(WxPayService::class.java)
        val orderResult: WxPayMpOrderResult = wxPayService.createOrder(unifiedOrderRequest)

        CoroutineScope(Dispatchers.IO).launch {
            callback(unifiedOrderRequest, orderResult)
        }

        return orderResult
    }

    /**
     * 退款
     * @param totalAmount 充值金额
     * @param amount 退款金额
     * @param wxPayNo 微信充值订单号
     * @param zyRefundNo 掌医退款订单号
     */
    @Throws(WxPayException::class)
    fun refund(
        totalAmount: Int,
        amount: Int,
        wxPayNo: String,
        zyRefundNo: String,
    ): WxPayRefundResult {
        val serverConfig: ServerConfig = SpringUtils.getBean(ServerConfig::class.java)
        val refundRequest: WxPayRefundRequest =
            WxPayRefundRequest.newBuilder()
                .transactionId(wxPayNo)
                .outRefundNo(zyRefundNo)
                .totalFee(totalAmount)
                .refundFee(amount)
                .notifyUrl(serverConfig.onWxRefundUrl)
                .build()
        val wxPayService = SpringUtils.getBean(WxPayService::class.java)
        return wxPayService.refund(refundRequest)
    }

    fun downloadAndSaveWxBill(billDate: LocalDate): WxPayBillResult? {
        return try {
            val wxPayService = SpringUtils.getBean(WxPayService::class.java)!! as WxPayServiceImpl
            wxPayService.downloadAndSaveBill(billDate = billDate)
        } catch (e: WxPayException) {
            if (e.returnMsg == "No Bill Exist") {
                FileUtil.writeUtf8String("当日无账单", buildBillPath(billDate))
                val wxPayBillResult = WxPayBillResult()
                wxPayBillResult.totalRecord = "0"
                wxPayBillResult.totalFee = "0.00"
                wxPayBillResult.totalAmount = "0.00"
                wxPayBillResult.totalRefundFee = "0.00"
                wxPayBillResult
            } else {
                throw e
            }
        }
    }

    fun downloadAndSaveMipBill(billDate: LocalDate): File? {
        return try {
            val wxInsurancePayDownloadBillResult = getWxInsurancePayService().downloadBill(
                WxInsurancePayDownloadBillRequest(
                    billDate = billDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                    billType = "ALL"
                )
            )
            val file = File(buildMipBillPath(billDate))
            HttpDownloader.downloadFile(wxInsurancePayDownloadBillResult.downloadUrl, file)
            file
        } catch (e: Exception) {
            null
        }
    }

    fun getOrDownloadBillFile(billDate: LocalDate): File {
        val path: String = buildBillPath(billDate)
        val file = File(path)
        if (file.exists()) {
            return file
        }

        downloadAndSaveWxBill(billDate)
        return File(path)
    }

    fun getOrDownloadMipBillFile(billDate: LocalDate): File {
        val path: String = buildMipBillPath(billDate)
        val file = File(path)
        if (file.exists()) {
            return file
        }

        downloadAndSaveMipBill(billDate)
        return File(path)
    }

    /**
     * 发送订阅消息
     */
    fun sendSubscribeMessage(
        messageId: String?,
        messagePage: String?,
        messageDataList: List<WxMaSubscribeMessage.MsgData>,
        openid: String,
        companionId: String,
    ): WxSubscribeMessage? {
        if (messageId.isNullOrBlank()) {
            return null
        }

        val wxSubscribeMessageConfiguration = SpringUtils.getBean(WxSubscribeMessageConfiguration::class.java)
        val type: String = when (messageId) {
            wxSubscribeMessageConfiguration.yuyueId -> "Yuyue"
            wxSubscribeMessageConfiguration.cancelYuyueId -> "CancelYuyue"
            wxSubscribeMessageConfiguration.queuePositionReminderId -> "QueuePositionReminder"
            else -> "Unknown"
        }

        val wxSubscribeMessageService = SpringUtils.getBean(IWxSubscribeMessageService::class.java)
        if (wxSubscribeMessageService.existsByCompanionIdAndTypeAndSendStatus(companionId, type, true)) {
            return null
        }

        val subscribeMessage: WxMaSubscribeMessage = WxMaSubscribeMessage.builder().apply {
            if (!messagePage.isNullOrBlank()) {
                this.page(messagePage)
            }

            this.templateId(messageId)
            this.toUser(openid)
            this.data(messageDataList)
        }.build()
        val now = Date()
        val data = JacksonKit.writeValueAsString(subscribeMessage.data)

        val wxSubscribeMessage = WxSubscribeMessage.builder()
            .templateId(subscribeMessage.templateId)
            .openid(subscribeMessage.toUser)
            .data(data)
            .type(type)
            .companionId(companionId)
            .sendTime(LocalDateTime.now())
            .sendStatus(true)
            .error("OK")
            .build()

        try {
            val wxMaService = SpringUtils.getBean(WxMaService::class.java)
            wxMaService.msgService.sendSubscribeMsg(subscribeMessage)
        } catch (e: Exception) {
            wxSubscribeMessage.sendStatus = false
            wxSubscribeMessage.error = e.message ?: "未知错误"
        }

        wxSubscribeMessageService.save(wxSubscribeMessage)
        return wxSubscribeMessage
    }

    fun newWxMaSubscribeMessagePhraseData(name: String, value: String): WxMaSubscribeMessage.MsgData {
        return WxMaSubscribeMessage.MsgData(name, StringKit.removeIfNotChinese(value).take(5))
    }

    fun newWxMaSubscribeMessageThingData(name: String, value: String): WxMaSubscribeMessage.MsgData {
        return WxMaSubscribeMessage.MsgData(name, value.take(20))
    }

    fun newWxMaSubscribeMessageNameData(name: String, value: String): WxMaSubscribeMessage.MsgData {
        return WxMaSubscribeMessage.MsgData(name, StringKit.removeIfNotChinese(value).take(10))
    }

    private var wxInsurancePayService: WxInsurancePayService? = null

    @Synchronized
    fun getWxInsurancePayService(): WxInsurancePayService {
        if (wxInsurancePayService == null) {
            val wxPayService: WxPayService = SpringUtils.getBean(WxPayService::class.java)
            val wxMaService: WxMaService = SpringUtils.getBean(WxMaService::class.java)
            val wxPayConfig: WxPayConfig = WxPayConfig().apply {
                val originalPayConfig: WxPayConfig = wxPayService.config
                this.appId = originalPayConfig.appId
                this.mchId = originalPayConfig.mchId
                this.mchKey = WeixinMipSetting.ma.mipKey
                this.keyPath = originalPayConfig.keyPath
            }
            wxInsurancePayService = WxInsurancePayService(wxPayConfig, {
                wxMaService.getAccessToken(it)
            }, wxPayService)
        }
        return wxInsurancePayService!!
    }

    fun idCardOcr(file: File): WxOcrIdCardResult {
        val wxMaService: WxMaService = SpringUtils.getBean(WxMaService::class.java)
        val wxMaOcrService: WxOcrService = wxMaService.ocrService
        return wxMaOcrService.idCard(file)
    }

}

private fun buildBillPath(billDate: LocalDate): String =
    Constants.WX_BILL_DIR + File.separatorChar + "${formatWxBillDate(billDate)}_wxbill.csv"

private fun buildMipBillPath(billDate: LocalDate): String =
    Constants.WX_BILL_DIR + File.separatorChar + "${formatWxBillDate(billDate)}_mipbill.csv"

private fun formatWxBillDate(billDate: LocalDate): String =
    billDate.format(DateTimeFormatter.ofPattern(DatePattern.PURE_DATE_PATTERN))

private fun buildWxPayDownloadRequest(
    billDate: LocalDate,
    billType: String = "ALL",
    tarType: String? = null,
    deviceInfo: String? = null,
): WxPayDownloadBillRequest {
    val wxPayDownloadBillRequest = WxPayDownloadBillRequest()
    wxPayDownloadBillRequest.billType = billType
    wxPayDownloadBillRequest.billDate = formatWxBillDate(billDate)
    wxPayDownloadBillRequest.tarType = tarType
    wxPayDownloadBillRequest.deviceInfo = deviceInfo
    return wxPayDownloadBillRequest
}

fun WxPayServiceImpl.downloadAndSaveBill(
    billDate: LocalDate,
    billType: String = "ALL",
    tarType: String? = null,
    deviceInfo: String? = null,
): WxPayBillResult? {
    val wxPayDownloadBillRequest = buildWxPayDownloadRequest(billDate, billType, tarType, deviceInfo)
    val responseContent = this.downloadRawBill(wxPayDownloadBillRequest)
    FileUtil.writeUtf8String(responseContent, buildBillPath(billDate))
    return if (StrUtil.isEmpty(responseContent)) {
        null
    } else {
        WxPayBillResult.fromRawBillResultString(responseContent, wxPayDownloadBillRequest.billType)
    }
}

fun WxPayBillInfo.isMenzhen(): Boolean = outTradeNo.startsWith(ServiceType.MZ.name)
fun WxPayBillInfo.isZhuyuan(): Boolean = outTradeNo.startsWith(ServiceType.ZY.name)
fun WxPayBillInfo.isPay(): Boolean = this.refundId.isNullOrBlank()
fun WxPayBillInfo.isRefund(): Boolean = !this.isPay()
fun WxPayBillResult.calculateTotalPayAmount(appId: String): BigDecimal =
    (this.billInfoList ?: emptyList()).filter { appId == it.appId && it.isPay() }.map { it.totalFee.toBigDecimal() }
        .sumOf { it }

fun WxPayBillResult.calculateMenzhenPayAmount(appId: String): BigDecimal =
    (this.billInfoList ?: emptyList()).filter { appId == it.appId && it.isPay() && it.isMenzhen() }
        .map { it.totalFee.toBigDecimal() }
        .sumOf { it }

fun WxPayBillResult.calculateZhuyuanPayAmount(appId: String): BigDecimal =
    (this.billInfoList ?: emptyList()).filter { appId == it.appId && it.isPay() && it.isZhuyuan() }
        .map { it.totalFee.toBigDecimal() }
        .sumOf { it }

fun WxPayBillResult.calculateTotalRefundAmount(appId: String): BigDecimal =
    (this.billInfoList ?: emptyList()).filter { appId == it.appId && it.isRefund() }
        .map { it.settlementRefundFee.toBigDecimal() }
        .sumOf { it }

fun WxPayBillResult.calculateMenzhenRefundAmount(appId: String): BigDecimal =
    (this.billInfoList ?: emptyList()).filter { appId == it.appId && it.isRefund() && it.isMenzhen() }
        .map { it.settlementRefundFee.toBigDecimal() }
        .sumOf { it }

fun WxPayBillResult.calculateZhuyuanRefundAmount(appId: String): BigDecimal =
    (this.billInfoList ?: emptyList()).filter { appId == it.appId && it.isRefund() && it.isZhuyuan() }
        .map { it.settlementRefundFee.toBigDecimal() }
        .sumOf { it }