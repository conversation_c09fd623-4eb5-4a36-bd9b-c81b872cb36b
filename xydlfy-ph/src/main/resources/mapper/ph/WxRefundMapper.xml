<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.WxRefundMapper">

    <resultMap type="space.lzhq.ph.domain.WxRefund" id="WxRefundResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="type" column="type"/>
        <result property="openid" column="openid"/>
        <result property="patientNo" column="patient_no"/>
        <result property="jzCardNo" column="jz_card_no"/>
        <result property="zhuyuanNo" column="zhuyuan_no"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="amount" column="amount"/>
        <result property="zyPayNo" column="zy_pay_no"/>
        <result property="wxPayNo" column="wx_pay_no"/>
        <result property="zyRefundNo" column="zy_refund_no"/>
        <result property="wxRefundNo" column="wx_refund_no"/>
        <result property="hisTradeStatus" column="his_trade_status"/>
        <result property="wxTradeStatus" column="wx_trade_status"/>
        <result property="manualRefundState" column="manual_refund_state"/>
    </resultMap>

    <sql id="selectWxRefundVo">
        select id,
               create_time,
               type,
               openid,
               patient_no,
               jz_card_no,
               zhuyuan_no,
               total_amount,
               amount,
               zy_pay_no,
               wx_pay_no,
               zy_refund_no,
               wx_refund_no,
               his_trade_status,
               wx_trade_status,
               manual_refund_state
        from ph_wx_refund
    </sql>

    <select id="selectWxRefundList" parameterType="WxRefund" resultMap="WxRefundResult">
        <include refid="selectWxRefundVo"/>
        <where>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and create_time between #{params.beginCreateTime} and #{params.endCreateTime}
            </if>
            <if test="patientNo != null  and patientNo != ''">and patient_no = #{patientNo}</if>
            <if test="jzCardNo != null  and jzCardNo != ''">and jz_card_no = #{jzCardNo}</if>
            <if test="zhuyuanNo != null  and zhuyuanNo != ''">and zhuyuan_no = #{zhuyuanNo}</if>
            <if test="zyPayNo != null  and zyPayNo != ''">and zy_pay_no = #{zyPayNo}</if>
            <if test="wxPayNo != null  and wxPayNo != ''">and wx_pay_no = #{wxPayNo}</if>
            <if test="zyRefundNo != null  and zyRefundNo != ''">and zy_refund_no = #{zyRefundNo}</if>
            <if test="wxRefundNo != null  and wxRefundNo != ''">and wx_refund_no = #{wxRefundNo}</if>
            <if test="manualRefundState != null ">and manual_refund_state = #{manualRefundState}</if>
            <if test="params.filterErrorOrder != null and params.filterErrorOrder != ''">
                and (his_trade_status = '退款成功' and wx_trade_status != '退款成功')
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectWxRefundById" parameterType="Long" resultMap="WxRefundResult">
        <include refid="selectWxRefundVo"/>
        where id = #{id}
    </select>

    <select id="selectOneByWxPayNo" parameterType="String" resultMap="WxRefundResult">
        <include refid="selectWxRefundVo"/>
        where wx_pay_no = #{wxPayNo}
    </select>

    <select id="sumAmount" parameterType="WxRefund" resultType="Long">
        select sum(amount) as sumAmount
        from ph_wx_refund
        where create_time between #{params.beginCreateTime} and #{params.endCreateTime}
          and wx_refund_no != ''
    </select>

    <insert id="insertWxRefund" parameterType="WxRefund" useGeneratedKeys="true" keyProperty="id">
        insert into ph_wx_refund
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null ">create_time,</if>
            <if test="type != null">type,</if>
            <if test="openid != null">openid,</if>
            <if test="patientNo != null">patient_no,</if>
            <if test="jzCardNo != null">jz_card_no,</if>
            <if test="zhuyuanNo != null">zhuyuan_no,</if>
            <if test="totalAmount != null ">total_amount,</if>
            <if test="amount != null ">amount,</if>
            <if test="zyPayNo != null">zy_pay_no,</if>
            <if test="wxPayNo != null">wx_pay_no,</if>
            <if test="zyRefundNo != null">zy_refund_no,</if>
            <if test="wxRefundNo != null">wx_refund_no,</if>
            <if test="hisTradeStatus != null">his_trade_status,</if>
            <if test="wxTradeStatus != null">wx_trade_status,</if>
            <if test="manualRefundState != null ">manual_refund_state,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null ">#{createTime},</if>
            <if test="type != null">#{type},</if>
            <if test="openid != null">#{openid},</if>
            <if test="patientNo != null">#{patientNo},</if>
            <if test="jzCardNo != null">#{jzCardNo},</if>
            <if test="zhuyuanNo != null">#{zhuyuanNo},</if>
            <if test="totalAmount != null ">#{totalAmount},</if>
            <if test="amount != null ">#{amount},</if>
            <if test="zyPayNo != null">#{zyPayNo},</if>
            <if test="wxPayNo != null">#{wxPayNo},</if>
            <if test="zyRefundNo != null">#{zyRefundNo},</if>
            <if test="wxRefundNo != null">#{wxRefundNo},</if>
            <if test="hisTradeStatus != null">#{hisTradeStatus},</if>
            <if test="wxTradeStatus != null">#{wxTradeStatus},</if>
            <if test="manualRefundState != null ">#{manualRefundState},</if>
        </trim>
    </insert>

    <update id="updateWxRefund" parameterType="WxRefund">
        update ph_wx_refund
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null ">create_time = #{createTime},</if>
            <if test="type != null">type = #{type},</if>
            <if test="openid != null">openid = #{openid},</if>
            <if test="patientNo != null">patient_no = #{patientNo},</if>
            <if test="jzCardNo != null">jz_card_no = #{jzCardNo},</if>
            <if test="zhuyuanNo != null">zhuyuan_no = #{zhuyuanNo},</if>
            <if test="totalAmount != null ">total_amount = #{totalAmount},</if>
            <if test="amount != null ">amount = #{amount},</if>
            <if test="zyPayNo != null">zy_pay_no = #{zyPayNo},</if>
            <if test="wxPayNo != null">wx_pay_no = #{wxPayNo},</if>
            <if test="zyRefundNo != null">zy_refund_no = #{zyRefundNo},</if>
            <if test="wxRefundNo != null">wx_refund_no = #{wxRefundNo},</if>
            <if test="hisTradeStatus != null">his_trade_status = #{hisTradeStatus},</if>
            <if test="wxTradeStatus != null">wx_trade_status = #{wxTradeStatus},</if>
            <if test="manualRefundState != null ">manual_refund_state = #{manualRefundState},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWxRefundById" parameterType="Long">
        delete
        from ph_wx_refund
        where id = #{id}
    </delete>

    <delete id="deleteWxRefundByIds" parameterType="String">
        delete from ph_wx_refund where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>