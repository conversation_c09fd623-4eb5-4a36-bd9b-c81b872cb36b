<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.PacsReportMapper">

    <resultMap id="PacsReportResult" type="space.lzhq.ph.domain.PacsReport">
        <result property="patientId" column="患者ID"/>
        <result property="patientType" column="就诊类型"/>
        <result property="reportNo" column="报告单号"/>
        <result property="reportName" column="报告名称"/>
        <result property="deptName" column="申请科室"/>
        <result property="doctorName" column="申请医生"/>
        <result property="orderTime" column="执行时间"/>
        <result property="itemName" column="检查项目"/>
        <result property="reportdescribe" column="影像所见"/>
        <result property="reportdiagnose" column="诊断结论"/>
        <result property="reportUrl" column="PDF地址"/>
    </resultMap>

    <sql id="selectPacsReportVo">
        select * from pacs_result
    </sql>

    <select id="selectPacsReportList" parameterType="space.lzhq.ph.domain.PacsReport" resultMap="PacsReportResult">
        <include refid="selectPacsReportVo"/>
        <where>
            <if test="patientId != null and patientId != ''">
                and 患者ID = #{patientId}
            </if>
            <if test="patientType != null and patientType != ''">
                and 就诊类型 = #{patientType}
            </if>
            <if test="params.days != null and params.days != '' "> and 执行时间 > sysdate-#{params.days}</if>
        </where>
        order by 执行时间 desc
    </select>
</mapper>