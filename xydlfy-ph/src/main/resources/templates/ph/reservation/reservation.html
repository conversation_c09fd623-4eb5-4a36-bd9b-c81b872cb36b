<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:insert="~{include :: header('预约记录列表')}"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>操作类型：</label>
                            <select name="operationType" th:with="type=${@dict.getType('reservation_operation_type')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li class="select-time">
                            <label>操作时间：</label>
                            <input type="text" class="time-input" id="startTime" placeholder="开始时间"
                                   name="params[beginOperationTime]"/>
                            <span>-</span>
                            <input type="text" class="time-input" id="endTime" placeholder="结束时间"
                                   name="params[endOperationTime]"/>
                        </li>
                        <li>
                            <label>身份证号：</label>
                            <input type="text" name="idCardNo"/>
                        </li>
                        <li>
                            <label>就诊卡号：</label>
                            <input type="text" name="patientId"/>
                        </li>
                        <li>
                            <label>姓名：</label>
                            <input type="text" name="name"/>
                        </li>
                        <li>
                            <label>手机号：</label>
                            <input type="text" name="mobile"/>
                        </li>
                        <li>
                            <label>就诊日期：</label>
                            <input type="text" class="time-input" placeholder="请选择就诊日期" name="jzDate"/>
                        </li>
                        <li>
                            <label>科室编码：</label>
                            <input type="text" name="departmentCode"/>
                        </li>
                        <li>
                            <label>科室名称：</label>
                            <input type="text" name="departmentName"/>
                        </li>
                        <li>
                            <label>医生编码：</label>
                            <input type="text" name="doctorCode"/>
                        </li>
                        <li>
                            <label>预约号：</label>
                            <input type="text" name="reservationNumber"/>
                        </li>
                        <li>
                            <label>交易流水号：</label>
                            <input type="text" name="outTradeNo"/>
                        </li>
                        <li>
                            <label>操作结果：</label>
                            <input type="text" name="operationResult"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="ph:reservation:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:insert="~{include :: footer}"/>
<script th:inline="javascript">
    var operationTypeDatas = [[${@dict.getType('reservation_operation_type')}]];
    var operationResultDatas = [[${@dict.getType('reservation_operation_result')}]];
    var yuyueStatusDatas = [[${@dict.getType('yuyue_status_type')}]];
    var queryWXOrderFlag = [[${@permission.hasPermi('ph:reservation:query')}]];
    var requestHisRechargeFlag = [[${@permission.hasPermi('ph:payment:requestHisRecharge')}]];
    var approveHisRechargeFlag = [[${@permission.hasPermi('ph:payment:approveHisRecharge')}]];
    var refundFlag = [[${@permission.hasPermi('ph:reservation:refund')}]];
    var revokeFlag = [[${@permission.hasPermi('ph:reservation:revoke')}]];
    var prefix = ctx + "ph/reservation";

    $(function () {
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            modalName: "预约记录",
            columns: [{
                checkbox: true
            },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'operationType',
                    title: '操作类型',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(operationTypeDatas, value);
                    }
                },
                {
                    field: 'operationTime',
                    title: '操作时间'
                },
                {
                    field: 'idCardNo',
                    title: '身份证号'
                },
                {
                    field: 'patientId',
                    title: '就诊卡号'
                },
                {
                    field: 'name',
                    title: '姓名'
                },
                {
                    field: 'mobile',
                    title: '手机号'
                },
                {
                    field: 'jzDate',
                    title: '就诊日期'
                },
                {
                    field: 'jzTime',
                    title: '就诊时间'
                },
                {
                    field: 'departmentCode',
                    title: '科室编码'
                },
                {
                    field: 'departmentName',
                    title: '科室名称'
                },
                {
                    field: 'departmentLocation',
                    title: '科室位置'
                },
                {
                    field: 'doctorCode',
                    title: '医生编码'
                },
                {
                    field: 'visitNumber',
                    title: '顺序号'
                },
                {
                    field: 'reservationNumber',
                    title: '预约号'
                },
                {
                    field: 'outTradeNo',
                    title: '交易流水号'
                },
                {
                    field: 'operationResult',
                    title: '操作结果',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(operationResultDatas, value);
                    }
                },
                {
                    field: 'status',
                    title: '挂号状态',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(yuyueStatusDatas, value);
                    }
                },
                {
                    field: 'operationDesc',
                    title: '操作描述'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + queryWXOrderFlag + '" href="javascript:void(0)" onclick="queryWXOrder(\'' + row.id + '\')"><i class="fa fa-refresh"></i>查询微信订单状态</a> ');

                        actions.push('<a class="btn btn-success btn-xs ' + revokeFlag + '" href="javascript:void(0)" onclick="revoke(\'' + row.id + '\')"><i class="fa fa-refresh"></i>释放号源/退款</a> ');
                        actions.push('<a class="btn btn-success btn-xs ' + refundFlag + '" href="javascript:void(0)" onclick="refund(\'' + row.id + '\')"><i class="fa fa-refresh"></i>退号/退款</a> ');
                        if (row['wxTradeStatus'] === '支付成功' && row['hisTradeStatus'] !== '充值成功' && row['manualPayState'] == 0) {
                            actions.push('<a class="btn btn-success btn-xs ' + requestHisRechargeFlag + '" href="javascript:void(0)" onclick="requestHisRecharge(\'' + row.id + '\')"><i class="fa fa-paper-plane-o"></i>申请HIS充值</a> ');
                        }
                        if (row['wxTradeStatus'] === '支付成功' && row['hisTradeStatus'] !== '充值成功' && row['manualPayState'] == 1) {
                            actions.push('<a class="btn btn-success btn-xs ' + approveHisRechargeFlag + '" href="javascript:void(0)" onclick="approveHisRecharge(\'' + row.id + '\')"><i class="fa fa-check"></i>放行HIS充值</a> ');
                        }
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });

    function queryWXOrder(id) {
        $.operate.get(prefix + '/query/' + id, function (resp) {
            layer.open({
                type: 1,
                skin: 'layui-layer-rim',
                area: ['80%', '80%'],
                title: '查询订单状态',
                content: '<pre class="layui-code" lay-title="JSON" lay-encode="true">' + JSON.stringify(resp, null, 4) + '</pre>',
                success: function () {
                    layui.use('code', function () {
                        layui.code();
                    });
                }
            });
        });
    }

    function revoke(id) {
        $.operate.get(prefix + '/revoke/' + id, function (resp) {
            layer.open({
                type: 1,
                skin: 'layui-layer-rim',
                area: ['80%', '80%'],
                title: '释放号源/退款',
                content: '<pre class="layui-code" lay-title="JSON" lay-encode="true">' + JSON.stringify(resp, null, 4) + '</pre>',
                success: function () {
                    layui.use('code', function () {
                        layui.code();
                    });
                }
            });
        });
    }

    function refund(id) {
        $.operate.get(prefix + '/refund/' + id, function (resp) {
            layer.open({
                type: 1,
                skin: 'layui-layer-rim',
                area: ['80%', '80%'],
                title: '退号/退款',
                content: '<pre class="layui-code" lay-title="JSON" lay-encode="true">' + JSON.stringify(resp, null, 4) + '</pre>',
                success: function () {
                    layui.use('code', function () {
                        layui.code();
                    });
                }
            });
        });
    }
</script>
</body>
</html>
