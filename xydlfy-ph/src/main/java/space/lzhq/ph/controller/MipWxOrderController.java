package space.lzhq.ph.controller;

import org.dromara.hutool.core.exception.ExceptionUtil;
import org.dromara.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.binarywang.wxpay.bean.request.WxInsurancePayRefundRequest;
import com.github.binarywang.wxpay.bean.result.WxInsurancePayOrderQueryResult;
import com.github.binarywang.wxpay.bean.result.WxInsurancePayRefundResult;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import space.lzhq.ph.domain.MipWxOrder;
import space.lzhq.ph.ext.WeixinExt;
import space.lzhq.ph.service.IMipWxOrderService;

import java.util.List;

/**
 * 微信医保订单Controller
 */
@Controller
@RequestMapping("/ph/mipWxOrder")
public class MipWxOrderController extends BaseController {

    private IMipWxOrderService mipWxOrderService;

    public MipWxOrderController(IMipWxOrderService mipWxOrderService) {
        this.mipWxOrderService = mipWxOrderService;
    }

    @RequiresPermissions("ph:mipWxOrder:view")
    @GetMapping()
    public String mipWxOrder() {
        return "ph/mipWxOrder/mipWxOrder";
    }

    /**
     * 查询微信医保订单列表
     */
    @RequiresPermissions("ph:mipWxOrder:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(MipWxOrder mipWxOrder) {
        startPage("id desc");
        List<MipWxOrder> list = mipWxOrderService.list(new QueryWrapper<>(mipWxOrder));
        return getDataTable(list);
    }

    /**
     * 导出微信医保订单列表
     */
    @RequiresPermissions("ph:mipWxOrder:export")
    @Log(title = "微信医保订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(MipWxOrder mipWxOrder) {
        List<MipWxOrder> list = mipWxOrderService.list(new QueryWrapper<>(mipWxOrder));
        ExcelUtil<MipWxOrder> util = new ExcelUtil<MipWxOrder>(MipWxOrder.class);
        return util.exportExcel(list, "mipWxOrder");
    }

    @RequiresPermissions("ph:mipWxOrder:queryTransaction")
    @GetMapping("/queryTransaction/{id}")
    @ResponseBody
    public AjaxResult queryTransaction(@PathVariable Long id) {
        MipWxOrder mipWxOrder = mipWxOrderService.getById(id);
        if (mipWxOrder == null) {
            return AjaxResult.error("订单不存在或已删除");
        }
        if (CharSequenceUtil.isBlank(mipWxOrder.getMedTransactionId())) {
            return AjaxResult.error("诊疗单ID为空，说明此单下单失败");
        }

        try {
            WxInsurancePayOrderQueryResult wxInsurancePayOrderQueryResult = WeixinExt.INSTANCE.getWxInsurancePayService().queryOrder(mipWxOrder.getMedTransactionId(), "");
            return AjaxResult.success(wxInsurancePayOrderQueryResult);
        } catch (Exception e) {
            return AjaxResult.error(ExceptionUtil.stacktraceToString(e));
        }
    }

    @RequiresPermissions("ph:mipWxOrder:refund")
    @GetMapping("/refund/{id}")
    @ResponseBody
    public AjaxResult refund(@PathVariable Long id) {
        MipWxOrder mipWxOrder = mipWxOrderService.getById(id);
        if (mipWxOrder == null) {
            return AjaxResult.error("订单不存在或已删除");
        }
        if (CharSequenceUtil.isBlank(mipWxOrder.getMedTransactionId())) {
            return AjaxResult.error("诊疗单ID为空，说明此单下单失败");
        }

        try {
            WxInsurancePayOrderQueryResult wxInsurancePayOrderQueryResult = WeixinExt.INSTANCE.getWxInsurancePayService().queryOrder(mipWxOrder.getMedTransactionId(), "");

            if (!"SUCCESS".equals(wxInsurancePayOrderQueryResult.getCashTradeStatus())) {
                String err = wxInsurancePayOrderQueryResult.getCashTradeStatusDescription();
                return AjaxResult.error("现金支付状态为%s，不可退款".formatted(err));
            }

            mipWxOrder.setHospOutRefundNo(mipWxOrder.getHospitalOutTradeNo() + "#R");
            WxInsurancePayRefundRequest refundRequest = new WxInsurancePayRefundRequest();


            refundRequest.setMedTransId(mipWxOrder.getMedTransactionId());
            refundRequest.setHospOutRefundNo(mipWxOrder.getHospOutRefundNo());
            refundRequest.setPartRefundType("CASH_ONLY");
            refundRequest.setPayOrderId(mipWxOrder.getPayOrderId());
            refundRequest.setRefReason("掌医小程序医保现金自费申请退款");

            WxInsurancePayRefundResult refundResult = WeixinExt.INSTANCE.getWxInsurancePayService().refund(refundRequest);
            mipWxOrderService.updateOnRefund(mipWxOrder, refundResult);
            return AjaxResult.success(refundResult);
        } catch (Exception e) {
            return AjaxResult.error(ExceptionUtil.stacktraceToString(e));
        }
    }
}
