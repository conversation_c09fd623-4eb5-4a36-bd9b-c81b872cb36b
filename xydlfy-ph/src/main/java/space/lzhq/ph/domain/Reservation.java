package space.lzhq.ph.domain;

import org.dromara.hutool.core.bean.BeanUtil;
import org.dromara.hutool.core.date.DateField;
import org.dromara.hutool.core.date.DateUtil;
import org.dromara.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.ParamsMapToQueryWrapperAppendable;
import org.mospital.jackson.JacksonKit;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 预约记录对象 ph_reservation
 *
 * <AUTHOR>
 * @date 2022-11-06
 */
@TableName(value = "ph_reservation", excludeProperty = {"searchValue", "createBy", "createTime", "updateBy", "updateTime", "remark"})
public class Reservation extends BaseEntity implements ParamsMapToQueryWrapperAppendable<Reservation> {

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 预约
     */
    @TableField(exist = false)
    public static final int OPERATION_TYPE_RESERVE = 0;

    /**
     * 取消预约
     */
    @TableField(exist = false)
    public static final int OPERATION_TYPE_CANCEL = 1;

    /**
     * 初始化状态
     */
    @TableField(exist = false)
    public static final int OPERATION_RESULT_INIT = 2;

    /**
     * 操作成功
     */
    @TableField(exist = false)
    public static final int OPERATION_RESULT_SUCCESS = 0;

    /**
     * 操作失败
     */
    @TableField(exist = false)
    public static final int OPERATION_RESULT_FAIL = 1;


    /**
     * 预约初始化状态
     */
    @TableField(exist = false)
    public static final Integer RESERVATION_STATUS_INIT = 0;

    /**
     * 挂号成功
     */
    @TableField(exist = false)
    public static final Integer RESERVATION_STATUS_FINISHED = 1;

    /**
     * 挂号失败
     */
    @TableField(exist = false)
    public static final Integer RESERVATION_STATUS_FAILED = 2;

    /**
     * 号源释放
     */
    @TableField(exist = false)
    public static final Integer RESERVATION_STATUS_RELEASED = 3;

    /**
     * 已使用（已看诊）
     */
    @TableField(exist = false)
    public static final Integer RESERVATION_STATUS_USED = 4;

    /**
     * 取消预约
     */
    @TableField(exist = false)
    public static final Integer RESERVATION_STATUS_CANCELED = 9;

    /**
     * 支付状态：不需要支付
     */
    @TableField(exist = false)
    public static final int PAYMENT_STATUS_FREE_OF_CHARGE = 0;

    /**
     * 支付状态：待支付
     */
    @TableField(exist = false)
    public static final int PAYMENT_STATUS_UNPAID = 1;

    /**
     * 支付状态：已支付
     */
    @TableField(exist = false)
    public static final int PAYMENT_STATUS_PAID = 2;

    /**
     * 支付状态：已退款
     */
    @TableField(exist = false)
    public static final int PAYMENT_STATUS_REFUND = 3;

    /**
     * 支付状态：取消支付
     */
    @TableField(exist = false)
    public static final int PAYMENT_STATUS_CANCELED = 9;

    /**
     * 支付方式：微信支付
     */
    @TableField(exist = false)
    public static final int PAYMENT_TYPE_WECHAT = 1;

    /**
     * 支付方式：支付宝
     */
    @TableField(exist = false)
    public static final int PAYMENT_TYPE_ALIPAY = 2;

    /**
     * 支付方式：微信医保移动支付
     */
    @TableField(exist = false)
    public static final int PAYMENT_TYPE_WECHAT_MIP = 3;

    /**
     * 支付方式：支付宝医保移动支付
     */
    @TableField(exist = false)
    public static final int PAYMENT_TYPE_ALIPAY_MIP = 4;

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 操作类型
     */
    @Excel(name = "操作类型", dictType = "reservation_operation_type")
    private Integer operationType;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "操作时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date operationTime;

    private String openid;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号")
    @TableField(whereStrategy = FieldStrategy.NOT_EMPTY)
    private String idCardNo;

    /**
     * 病人ID
     */
    @Excel(name = "病人ID")
    @TableField(whereStrategy = FieldStrategy.NOT_EMPTY)
    private String patientId;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    @TableField(whereStrategy = FieldStrategy.NOT_EMPTY)
    private String name;

    /**
     * 手机号
     */
    @Excel(name = "手机号")
    @TableField(whereStrategy = FieldStrategy.NOT_EMPTY)
    private String mobile;

    /**
     * 就诊日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "就诊日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date jzDate;

    /**
     * 就诊时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @Excel(name = "就诊时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date jzTime;

    /**
     * 挂号类型
     */
    @Excel(name = "挂号类型", dictType = "reservation_type")
    private String reservationType;

    /**
     * 科室编码
     */
    @Excel(name = "科室编码")
    @TableField(whereStrategy = FieldStrategy.NOT_EMPTY)
    private String departmentCode;

    /**
     * 科室名称
     */
    @Excel(name = "科室名称")
    @TableField(whereStrategy = FieldStrategy.NOT_EMPTY)
    private String departmentName;

    /**
     * 科室位置
     */
    @Excel(name = "科室位置")
    private String departmentLocation;

    /**
     * 医生编码
     */
    @Excel(name = "医生编码")
    @TableField(whereStrategy = FieldStrategy.NOT_EMPTY)
    private String doctorCode;

    /**
     * 医生姓名
     */
    @Excel(name = "医生姓名")
    private String doctorName;

    /**
     * 顺序号
     */
    @Excel(name = "顺序号")
    private String visitNumber;

    /**
     * 预约号
     */
    @Excel(name = "预约号")
    @TableField(whereStrategy = FieldStrategy.NOT_EMPTY)
    private String reservationNumber;

    /**
     * 操作结果
     */
    @Excel(name = "操作结果")
    @TableField(whereStrategy = FieldStrategy.NOT_NULL)
    private Integer operationResult;

    /**
     * 操作描述
     */
    @Excel(name = "操作描述")
    private String operationDesc;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private Integer status;

    /**
     * 支付宝医疗订单ID
     */
    private String alipayHospitalOrderId;

    private String scheduleId;

    private String prescriptionNo;

    /**
     * 预约号
     */
    private String subscriptionId;

    /**
     * 预约请求JSON串
     */
    private String subscriptionJson;

    /**
     * 商户支付单号
     */
    @TableField(whereStrategy = FieldStrategy.NOT_EMPTY)
    private String outTradeNo;

    /**
     * 挂号费
     */
    private BigDecimal clinicFee;

    /**
     * 支付状态: 0=不需要支付，1=待支付，2=已支付，3=支付失败，9=取消支付
     */
    private Integer paymentStatus;

    /**
     * 支付方式: 1=微信支付，2=支付宝，3=微信医保移动支付，4=支付宝医保移动支付
     */
    private Integer paymentType;

    /**
     * 签到提醒状态。0=未发送，1=发送成功，2=发送失败
     */
    private Integer checkInReminderStatus;

    private Long paymentId;

    public Reservation() {
    }

    public Reservation(Patient patient) {
        this.operationType = OPERATION_TYPE_RESERVE;
        this.operationTime = DateUtil.now().setField(DateField.MILLISECOND, 0);
        this.openid = patient.getOpenId();
        this.idCardNo = patient.getIdCardNo();
        this.patientId = patient.getPatientNo();
        this.name = patient.getName();
        this.mobile = patient.getMobile();
    }


    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }


    public String getSubscriptionId() {
        return subscriptionId;
    }

    public void setSubscriptionId(String subscriptionId) {
        this.subscriptionId = subscriptionId;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getSubscriptionJson() {
        return subscriptionJson;
    }

    public void setSubscriptionJson(String subscriptionJson) {
        this.subscriptionJson = subscriptionJson;
    }


    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationTime(Date operationTime) {
        this.operationTime = operationTime;
    }

    public Date getOperationTime() {
        return operationTime;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public void setIdCardNo(String idCardNo) {
        this.idCardNo = idCardNo;
    }

    public String getIdCardNo() {
        return idCardNo;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public String getPatientId() {
        return patientId;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getMobile() {
        return mobile;
    }

    public void setJzDate(Date jzDate) {
        this.jzDate = jzDate;
    }

    public Date getJzDate() {
        return jzDate;
    }

    public void setJzTime(Date jzTime) {
        this.jzTime = jzTime;
    }

    public Date getJzTime() {
        return jzTime;
    }

    public String getReservationType() {
        return reservationType;
    }

    public void setReservationType(String reservationType) {
        this.reservationType = reservationType;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentLocation(String departmentLocation) {
        this.departmentLocation = departmentLocation;
    }

    public String getDepartmentLocation() {
        return departmentLocation;
    }

    public void setDoctorCode(String doctorCode) {
        this.doctorCode = doctorCode;
    }

    public String getDoctorCode() {
        return doctorCode;
    }

    public String getDoctorName() {
        return doctorName;
    }

    public void setDoctorName(String doctorName) {
        this.doctorName = doctorName;
    }

    public void setVisitNumber(String visitNumber) {
        this.visitNumber = visitNumber;
    }

    public String getVisitNumber() {
        return visitNumber;
    }

    public void setReservationNumber(String reservationNumber) {
        this.reservationNumber = reservationNumber;
    }

    public String getReservationNumber() {
        return reservationNumber;
    }

    public void setOperationResult(Integer operationResult) {
        this.operationResult = operationResult;
    }

    public Integer getOperationResult() {
        return operationResult;
    }

    public void setOperationDesc(String operationDesc) {
        this.operationDesc = operationDesc;
    }

    public String getOperationDesc() {
        return operationDesc;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getAlipayHospitalOrderId() {
        return alipayHospitalOrderId;
    }

    public void setAlipayHospitalOrderId(String alipayHospitalOrderId) {
        this.alipayHospitalOrderId = alipayHospitalOrderId;
    }

    public Integer getCheckInReminderStatus() {
        return checkInReminderStatus;
    }

    public void setCheckInReminderStatus(Integer checkInReminderStatus) {
        this.checkInReminderStatus = checkInReminderStatus;
    }

    public Integer getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(Integer paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public BigDecimal getClinicFee() {
        return clinicFee;
    }

    public void setClinicFee(BigDecimal clinicFee) {
        this.clinicFee = clinicFee;
    }

    public String getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(String scheduleId) {
        this.scheduleId = scheduleId;
    }

    public String getPrescriptionNo() {
        return prescriptionNo;
    }

    public void setPrescriptionNo(String prescriptionNo) {
        this.prescriptionNo = prescriptionNo;
    }

    public Integer getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(Integer paymentType) {
        this.paymentType = paymentType;
    }

    public Long getPaymentId() {
        return paymentId;
    }

    public void setPaymentId(Long paymentId) {
        this.paymentId = paymentId;
    }

    @Override
    public Map<String, Object> getParamsMap() {
        return getParams();
    }

    @Override
    public void append(Map.Entry<String, Object> paramEntry, QueryWrapper<Reservation> queryWrapper) {
        Object paramValue;
        if (paramEntry != null && (paramValue = paramEntry.getValue()) != null && CharSequenceUtil.isNotBlank(paramValue.toString())) {
            switch (paramEntry.getKey()) {
                case "beginOperationTime":
                    queryWrapper.ge("operation_time", paramValue);
                    break;
                case "endOperationTime":
                    queryWrapper.le("operation_time", paramValue);
                    break;
                default:
                    // do nothing
            }
        }
    }

    public Reservation toCancel() {
        Reservation cancel = new Reservation();
        BeanUtil.copyProperties(this, cancel);
        cancel.setId(null);
        cancel.setOperationType(OPERATION_TYPE_CANCEL);
        cancel.setOperationTime(DateUtil.now().setField(DateField.MILLISECOND, 0));
        cancel.setStatus(RESERVATION_STATUS_CANCELED);
        cancel.setOperationResult(OPERATION_RESULT_SUCCESS);
        return cancel;
    }

    @Override
    public String toString() {
        return JacksonKit.INSTANCE.writeValueAsString(this);
    }
}
