package space.lzhq.ph.domain;

import org.dromara.hutool.core.text.CharSequenceUtil;
import org.dromara.hutool.core.data.IdcardUtil;
import org.dromara.hutool.core.text.StrUtil;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.mospital.neusoft.result.QueryPatientResult;
import space.lzhq.ph.common.ClientType;

import java.io.Serial;
import java.util.Date;

/**
 * 就诊人对象 ph_patient
 *
 * @date 2020-05-25
 */
public class Patient extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户端
     */
    private Integer clientType;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号")
    private String idCardNo;

    /**
     * 患者索引号
     */
    @Excel(name = "患者索引号")
    private String patientNo;

    /**
     * 就诊卡号
     */
    @Excel(name = "就诊卡号")
    private String jzCardNo;

    /**
     * 门诊号
     */
    @Excel(name = "门诊号")
    private String menzhenNo;

    /**
     * 住院号
     */
    @Excel(name = "住院号")
    private String zhuyuanNo;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String name;

    /**
     * 性别：0=男，1=女
     */
    @Excel(name = "性别", readConverterExp = "0=男,1=女,2=未知")
    private Integer gender;

    /**
     * 手机号
     */
    @Excel(name = "手机号")
    private String mobile;

    /**
     * $column.columnComment
     */
    @Excel(name = "openid")
    private String openId;

    /**
     * $column.columnComment
     */
    @Excel(name = "unionid")
    private String unionId;

    /**
     * 是否默认卡
     */
    @Excel(name = "是否默认卡", readConverterExp = "1=是,0=否")
    private Integer active;

    /**
     * 电子健康卡主索引号
     */
    @Excel(name = "电子健康卡主索引号")
    private String empi;

    /**
     * 电子健康卡号
     */
    @Excel(name = "电子健康卡号")
    private String erhcCardNo;

    /**
     * 入院时间
     */
    @Excel(name = "入院时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date ruyuanTime;

    /**
     * 出院时间
     */
    @Excel(name = "出院时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date chuyuanTime;

    /**
     * 监护人身份证号
     */
    @Excel(name = "监护人身份证号")
    private String guarderIDCard;


    /**
     * 监护人手机号
     */
    @Excel(name = "监护人手机号")
    private String guarderPhoneNumber;

    /**
     * 监护人姓名
     */
    @Excel(name = "监护人姓名")
    private String guarderName;


    /**
     * 门诊余额
     */
    private Double menzhenBalance;

    /**
     * 住院余额
     */
    private Double zhuyuanBalance;

    public Patient() {

    }

    public Patient(Session session, QueryPatientResult menzhenPatient, String mobile) {
        this.clientType = session.getClientType();
        this.idCardNo = menzhenPatient.getIdCard();
        this.patientNo = menzhenPatient.getHisNo();
        this.jzCardNo = menzhenPatient.getHisNo();
        this.menzhenNo = "";
        this.zhuyuanNo = "";
        this.name = menzhenPatient.getRealName().trim();
        this.gender = "男".equals(menzhenPatient.getSex()) ? 0 : 1;
        this.mobile = CharSequenceUtil.trim(CharSequenceUtil.defaultIfNull(menzhenPatient.getPhone(), mobile));
        this.openId = session.getOpenId();
        this.unionId = session.getUnionId();
        this.active = 0;
        this.empi = "";
        this.erhcCardNo = "";
        this.menzhenBalance = null;
        this.zhuyuanBalance = null;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setClientType(Integer clientType) {
        this.clientType = clientType;
    }

    public Integer getClientType() {
        return clientType;
    }

    public ClientType getClientTypeEnum() {
        if (getClientType() == null) {
            return ClientType.WEIXIN;
        } else {
            return ClientType.Companion.fromCode(getClientType());
        }
    }

    public void setIdCardNo(String idCardNo) {
        this.idCardNo = idCardNo;
    }

    public String getIdCardNo() {
        return idCardNo;
    }

    public void setPatientNo(String patientNo) {
        this.patientNo = patientNo;
    }

    public String getPatientNo() {
        return patientNo;
    }

    public void setJzCardNo(String jzCardNo) {
        this.jzCardNo = jzCardNo;
    }

    public String getJzCardNo() {
        return jzCardNo;
    }

    public void setMenzhenNo(String menzhenNo) {
        this.menzhenNo = menzhenNo;
    }

    public String getMenzhenNo() {
        return menzhenNo;
    }

    public void setZhuyuanNo(String zhuyuanNo) {
        this.zhuyuanNo = zhuyuanNo;
    }

    public String getZhuyuanNo() {
        return zhuyuanNo;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public Integer getGender() {
        return gender;
    }

    public Boolean isMale() {
        return (StrUtil.isBlank(idCardNo) || !IdcardUtil.isValidCard(idCardNo)) ? false : IdcardUtil.getGender(getIdCardNo()) == 1;
    }

    public Boolean isFemale() {
        return (StrUtil.isBlank(idCardNo) || !IdcardUtil.isValidCard(idCardNo)) ? false : IdcardUtil.getGender(getIdCardNo()) == 0;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getMobile() {
        return mobile;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setActive(Integer active) {
        this.active = active;
    }

    public Integer getActive() {
        return active;
    }

    public void setEmpi(String empi) {
        this.empi = empi;
    }

    public String getEmpi() {
        return empi;
    }

    public void setErhcCardNo(String erhcCardNo) {
        this.erhcCardNo = erhcCardNo;
    }

    public String getErhcCardNo() {
        return erhcCardNo;
    }

    public Date getRuyuanTime() {
        return ruyuanTime;
    }

    public void setRuyuanTime(Date ruyuanTime) {
        this.ruyuanTime = ruyuanTime;
    }

    public Date getChuyuanTime() {
        return chuyuanTime;
    }

    public void setChuyuanTime(Date chuyuanTime) {
        this.chuyuanTime = chuyuanTime;
    }

    public void setMenzhenBalance(Double menzhenBalance) {
        this.menzhenBalance = menzhenBalance;
    }

    public Double getMenzhenBalance() {
        return this.menzhenBalance;
    }

    public Double getZhuyuanBalance() {
        return zhuyuanBalance;
    }

    public void setZhuyuanBalance(Double zhuyuanBalance) {
        this.zhuyuanBalance = zhuyuanBalance;
    }


    public String getGuarderName() {
        return guarderName;
    }

    public void setGuarderName(String guarderName) {
        this.guarderName = guarderName;
    }

    public String getGuarderPhoneNumber() {
        return guarderPhoneNumber;
    }

    public void setGuarderPhoneNumber(String guarderPhoneNumber) {
        this.guarderPhoneNumber = guarderPhoneNumber;
    }


    public String getGuarderIDCard() {
        return guarderIDCard;
    }

    public void setGuarderIDCard(String guarderIDCard) {
        this.guarderIDCard = guarderIDCard;
    }

    public boolean hasErhcCard() {
        return !erhcCardNo.isEmpty();
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("clientType", getClientType())
                .append("idCardNo", getIdCardNo())
                .append("patientNo", getPatientNo())
                .append("jzCardNo", getJzCardNo())
                .append("menzhenNo", getMenzhenNo())
                .append("zhuyuanNo", getZhuyuanNo())
                .append("name", getName())
                .append("gender", getGender())
                .append("mobile", getMobile())
                .append("openId", getOpenId())
                .append("unionId", getUnionId())
                .append("createTime", getCreateTime())
                .append("active", getActive())
                .append("empi", getEmpi())
                .append("erhcCardNo", getErhcCardNo())
                .append("guarderPhoneNumber", getGuarderPhoneNumber())
                .append("guarderName", getGuarderName())
                .append("guarderIDCard", getGuarderIDCard())
                .toString();
    }
}