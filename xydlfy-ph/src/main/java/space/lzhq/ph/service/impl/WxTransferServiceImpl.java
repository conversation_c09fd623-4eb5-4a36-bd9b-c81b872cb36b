package space.lzhq.ph.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.binarywang.wxpay.bean.transfer.TransferBillsGetResult;
import com.github.binarywang.wxpay.bean.transfer.TransferBillsRequest;
import com.github.binarywang.wxpay.bean.transfer.TransferBillsResult;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.ruoyi.common.config.ServerConfig;
import com.ruoyi.common.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.hutool.core.text.CharSequenceUtil;
import org.jetbrains.annotations.NotNull;
import org.mospital.dongruan.qingtui.DongruanQingtuiService;
import org.mospital.dongruan.qingtui.ThirdPartyClearanceRequest;
import org.mospital.dongruan.qingtui.ThirdPartyClearanceResponse;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import space.lzhq.ph.common.PaymentKit;
import space.lzhq.ph.common.ServiceType;
import space.lzhq.ph.domain.WxTransfer;
import space.lzhq.ph.exception.CardRefundException;
import space.lzhq.ph.exception.WxTransferException;
import space.lzhq.ph.mapper.WxTransferMapper;
import space.lzhq.ph.service.IWxTransferService;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@Validated
public class WxTransferServiceImpl extends ServiceImpl<WxTransferMapper, WxTransfer> implements IWxTransferService {

    private final WxPayService wxPayService;
    private final ServerConfig serverConfig;

    public WxTransferServiceImpl(WxPayService wxPayService, ServerConfig serverConfig) {
        this.wxPayService = wxPayService;
        this.serverConfig = serverConfig;
    }

    @Override
    public List<WxTransfer> listByApplicationIdAndCardNo(long applicationId, @NotNull String cardNo) {
        return lambdaQuery()
                .eq(WxTransfer::getApplicationId, applicationId)
                .eq(WxTransfer::getCardNo, cardNo)
                .orderByDesc(WxTransfer::getId)
                .list();
    }

    public Optional<WxTransfer> getOneByOutBillNo(String outBillNo) {
        return lambdaQuery().eq(WxTransfer::getOutBillNo, outBillNo).oneOpt();
    }

    @Override
    public void requestTransfer(WxTransfer wxTransfer) throws CardRefundException {
        if (wxTransfer.getOutBillNo() != null) {
            throw new CardRefundException("转账单号已存在");
        }

        final String outBillNo = PaymentKit.INSTANCE.newOrderId(ServiceType.TF);
        int amountCents = (int) PaymentKit.INSTANCE.yuanToFen(wxTransfer.getAmount());
        String appId = wxPayService.getConfig().getAppId();

        TransferBillsRequest request = TransferBillsRequest.newBuilder()
                .appid(appId)
                .outBillNo(outBillNo)
                .transferSceneId("1000")
                .openid(wxTransfer.getOpenId())
                .notifyUrl(serverConfig.getOnWxTransferUrl())
                .userName("")
                .transferAmount(amountCents)
                .transferRemark("门诊预交金返还")
                .userRecvPerception("活动奖励")
                .transferSceneReportInfos(List.of(
                        TransferBillsRequest.TransferSceneReportInfo.newBuilder()
                                .infoType("活动名称")
                                .infoContent("门诊预交金余额清退")
                                .build(),
                        TransferBillsRequest.TransferSceneReportInfo.newBuilder()
                                .infoType("奖励说明")
                                .infoContent("门诊预交金返还")
                                .build()
                ))
                .build();
        wxTransfer.setOutBillNo(outBillNo);

        try {
            TransferBillsResult transferResult = wxPayService.getTransferService().transferBills(request);
            updateTransferInfo(wxTransfer, transferResult);
        } catch (WxPayException e) {
            log.error(e.getErrCodeDes(), e);
            wxTransfer.setCreateTime(LocalDateTime.now());
            wxTransfer.setTransferFailReason(e.getMessage());
        }

        updateById(wxTransfer);
    }

    @Override
    public void updateTransferState(WxTransfer wxTransfer) {
        if (wxTransfer == null || StringUtils.isBlank(wxTransfer.getOutBillNo())) {
            log.warn("更新转账状态失败：商户订单号为空");
            throw new IllegalArgumentException("商户订单号不能为空");
        }
        if (wxTransfer.getCreateTime().plusDays(30).isBefore(LocalDateTime.now())) {
            log.warn("单号：{}，错误：单据超过30天无法通过API查询", wxTransfer.getOutBillNo());
            return;
        }

        try {
            TransferBillsGetResult result = wxPayService.getTransferService()
                    .getBillsByOutBillNo(wxTransfer.getOutBillNo());

            updateTransferStateInfo(wxTransfer, result);
            updateById(wxTransfer);

            if (wxTransfer.isSuccessTransfer()) {
                notifyHis(wxTransfer);
            }

            log.info("转账状态更新成功，单号：{}，状态：{}",
                    wxTransfer.getOutBillNo(), result.getState());
        } catch (WxPayException e) {
            log.error("查询转账状态失败，单号：{}，错误：{}",
                    wxTransfer.getOutBillNo(), e.getMessage(), e);
            throw new WxTransferException("查询转账状态失败", e);
        }
    }

    @Override
    public void notifyHis(WxTransfer wxTransfer) {
        if (wxTransfer.isSuccessNotifyHis()) {
            return;
        }

        ThirdPartyClearanceResponse response = DongruanQingtuiService.Companion.getMe().writeBackThirdPartyClearance(
                new ThirdPartyClearanceRequest(
                        wxTransfer.getCardNo(),
                        wxTransfer.getOutBillNo(),
                        wxTransfer.getUpdateTime(),
                        wxTransfer.getAmount(),
                        wxTransfer.getHisRegisterId(),
                        wxTransfer.getName() + "," + wxTransfer.getMobile() + "," + wxTransfer.getAmount()
                )
        );

        wxTransfer.setHisNotificationTime(LocalDateTime.now());
        if (response.isOk()) {
            wxTransfer.setHisNotificationState(1);
            wxTransfer.setHisNotificationResult("OK");
        } else {
            wxTransfer.setHisNotificationState(2);
            wxTransfer.setHisNotificationResult(response.getMsg());
        }
        updateById(wxTransfer);
    }

    private void updateTransferInfo(WxTransfer wxTransfer, TransferBillsResult transferResult) {
        wxTransfer.setTransferBillNo(transferResult.getTransferBillNo());
        wxTransfer.setTransferState(transferResult.getState());
        wxTransfer.setTransferFailReason(CharSequenceUtil.emptyIfNull(transferResult.getFailReason()).toString());
        wxTransfer.setTransferPackageInfo(transferResult.getPackageInfo());
        wxTransfer.setCreateTime(parseDateTime(transferResult.getCreateTime()));
    }

    private void updateTransferStateInfo(WxTransfer wxTransfer, TransferBillsGetResult result) {
        wxTransfer.setTransferState(result.getState());
        wxTransfer.setTransferFailReason(result.getFailReason());

        if (StringUtils.isBlank(wxTransfer.getTransferBillNo())) {
            wxTransfer.setTransferBillNo(result.getTransferBillNo());
        }

        updateTransferTimes(wxTransfer, result);
    }

    private void updateTransferTimes(WxTransfer wxTransfer, TransferBillsGetResult result) {
        if (StringUtils.isNotBlank(result.getCreateTime())) {
            LocalDateTime parsedCreateTime = parseDateTime(result.getCreateTime());
            if (parsedCreateTime != null) {
                wxTransfer.setCreateTime(parsedCreateTime);
            }
        }

        if (StringUtils.isNotBlank(result.getUpdateTime())) {
            LocalDateTime parsedUpdateTime = parseDateTime(result.getUpdateTime());
            if (parsedUpdateTime != null) {
                wxTransfer.setUpdateTime(parsedUpdateTime);
            }
        }
    }

    private LocalDateTime parseDateTime(String dateTimeStr) {
        if (StringUtils.isBlank(dateTimeStr)) {
            log.warn("日期时间字符串为空");
            return null;
        }

        try {
            return LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ISO_OFFSET_DATE_TIME);
        } catch (Exception e) {
            log.error("解析时间失败: {}, 错误: {}", dateTimeStr, e.getMessage());
            return null;
        }
    }

}
