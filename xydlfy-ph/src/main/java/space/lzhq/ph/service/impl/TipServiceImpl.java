package space.lzhq.ph.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.Tip;
import space.lzhq.ph.mapper.TipMapper;
import space.lzhq.ph.service.ITipService;

import java.util.Collections;
import java.util.List;

@Service
public class TipServiceImpl extends ServiceImpl<TipMapper, Tip> implements ITipService {

    @Override
    public List<Tip> listByCodes(List<String> codes) {
        if (codes == null || codes.isEmpty()) {
            return Collections.emptyList();
        }

        return lambdaQuery().in(Tip::getCode, codes).list();
    }

}
