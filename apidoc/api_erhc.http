###    /**
###     *  民族代码
###     */
GET {{host}}/open/dict/queryByType?type=minzu
Content-Type: application/x-www-form-urlencoded
Authorization: {{sid}}

###    /**
###     *  职业代码
###     */
GET {{host}}/open/dict/queryByType?type=his_profession
Content-Type: application/x-www-form-urlencoded
Authorization: {{sid}}


###    /**
###     * 查询或注册电子健康卡
###     */
POST {{host}}/api/erhcCard/getOrRegister
Content-Type: application/x-www-form-urlencoded
Authorization: {{sid}}

name=刘晶&mobile=13619936079&nationCode=1&idCardNo=110101200007285509


###    /**
###     * 查询或创建就诊卡
###     * careerCode 职业代码，如果不传默认是90，其他
###     */
POST {{host}}/api/erhcCard/getOrRegisterJzCard
Content-Type: application/x-www-form-urlencoded
Authorization: {{sid}}

name=刘晶&mobile=13619936079&nationCode=1&idCardNo=110101200007285509&careerCode=90

###    /**
###     * 绑卡
###     */
POST {{host}}/api/erhcCard/bind
Content-Type: application/x-www-form-urlencoded
Authorization: {{sid}}

idCardNo=659001198712305913&erhcCardNo=30A9CCF8B86F30DED24EAE84C5467DD9BBE68A0EDBC478B6E47F8A0771A003DE&empi=71F6DDBCFDDAA3B4E6ADCAE0FCD40FA8ACC04B9DF75BEF460C378A96E20DBBF2

